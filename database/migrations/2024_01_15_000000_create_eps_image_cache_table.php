<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEpsImageCacheTable extends Migration
{
    public function up()
    {
        Schema::create('eps_image_cache', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('shopify_product_id');
            $table->unsignedBigInteger('shopify_variant_id')->nullable();
            $table->string('shopify_image_id');
            $table->string('variation_specific_value')->nullable();
            $table->text('original_url');
            $table->text('eps_url');
            $table->timestamp('eps_uploaded_at')->useCurrent();
            $table->timestamp('use_by_date')->nullable();
            $table->timestamp('expires_at')->storedAs('eps_uploaded_at + INTERVAL 30 DAY');
            $table->string('picture_name')->nullable();
            $table->enum('picture_set', ['Standard', 'Supersize'])->default('Standard');
            $table->string('picture_format', 20)->nullable();
            $table->enum('upload_policy', ['Add', 'Replace'])->default('Add');
            $table->enum('eps_ack', ['Success', 'Failure', 'Warning'])->nullable();
            $table->text('eps_response')->nullable();
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            $table->enum('status', ['valid', 'expired', 'replaced'])->default('valid');
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes for high-volume performance
            $table->unique('shopify_image_id');
            $table->index(['shopify_product_id', 'status']);
            $table->index(['expires_at', 'status']);
            $table->index(['shopify_variant_id', 'variation_specific_value']);
            $table->index('eps_uploaded_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('eps_image_cache');
    }
} 