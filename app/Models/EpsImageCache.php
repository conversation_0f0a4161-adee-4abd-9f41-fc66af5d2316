<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class EpsImageCache extends Model
{
    protected $table = 'eps_image_cache';

    protected $fillable = [
        'shopify_product_id',
        'shopify_variant_id',
        'shopify_image_id',
        'variation_specific_value',
        'original_url',
        'eps_url',
        'eps_uploaded_at',
        'use_by_date',
        'picture_name',
        'picture_set',
        'picture_format',
        'upload_policy',
        'eps_ack',
        'eps_response',
        'usage_count',
        'last_used_at',
        'status',
        'notes'
    ];

    protected $casts = [
        'eps_uploaded_at' => 'datetime',
        'use_by_date' => 'datetime',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'usage_count' => 'integer',
    ];

    /**
     * Scope for valid (non-expired) cache entries
     */
    public function scopeValid(Builder $query): Builder
    {
        return $query->where('status', 'valid')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for entries by Shopify image ID
     */
    public function scopeByShopifyImageId(Builder $query, string $imageId): Builder
    {
        return $query->where('shopify_image_id', $imageId);
    }

    /**
     * Scope for entries by product ID
     */
    public function scopeByProductId(Builder $query, int $productId): Builder
    {
        return $query->where('shopify_product_id', $productId);
    }

    /**
     * Check if the cache entry is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Mark cache entry as used
     */
    public function markAsUsed(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Mark cache entry as expired
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Scope for variation-specific images
     */
    public function scopeWithVariations(Builder $query): Builder
    {
        return $query->whereNotNull('variation_specific_value');
    }

    /**
     * Scope for main product images (non-variation)
     */
    public function scopeMainImages(Builder $query): Builder
    {
        return $query->whereNull('variation_specific_value');
    }

    /**
     * Scope by variation value
     */
    public function scopeByVariationValue(Builder $query, string $value): Builder
    {
        return $query->where('variation_specific_value', $value);
    }

    /**
     * Get grouped variation images for a product
     */
    public static function getVariationImageGroups(int $shopifyProductId): array
    {
        $images = self::byProductId($shopifyProductId)
            ->valid()
            ->withVariations()
            ->get()
            ->groupBy('variation_specific_value');

        $groups = [];
        foreach ($images as $variationValue => $imageCollection) {
            $groups[$variationValue] = $imageCollection->pluck('eps_url')->toArray();
        }

        return $groups;
    }

    /**
     * Get main product images
     */
    public static function getMainProductImages(int $shopifyProductId): array
    {
        return self::byProductId($shopifyProductId)
            ->valid()
            ->mainImages()
            ->pluck('eps_url')
            ->toArray();
    }
} 