<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PerformanceMetricsService;
use App\Services\QueueHealthMonitoringService;
use App\Services\RateLimitingService;
use App\Services\CircuitBreakerService;
use App\Services\ApiHealthMonitoringService;
use App\Services\DatabaseOptimizationService;
use Illuminate\Http\Request;

class MonitoringController extends Controller
{
    public function __construct(
        private PerformanceMetricsService $performanceMetrics,
        private QueueHealthMonitoringService $queueHealth,
        private RateLimitingService $rateLimiter,
        private CircuitBreakerService $circuitBreaker,
        private ApiHealthMonitoringService $apiHealth,
        private DatabaseOptimizationService $dbOptimization
    ) {}

    /**
     * Get comprehensive dashboard data
     */
    public function dashboard(Request $request)
    {
        $hours = $request->get('hours', 24);
        
        return response()->json([
            'performance' => $this->performanceMetrics->getPerformanceDashboard($hours),
            'queue_health' => $this->queueHealth->getQueueHealthSummary(),
            'rate_limits' => [
                'eps' => $this->rateLimiter->getRateLimitStatus('eps'),
                'ebay' => $this->rateLimiter->getRateLimitStatus('ebay')
            ],
            'circuit_breakers' => [
                'eps_api' => $this->circuitBreaker->getMetrics('eps_api'),
                'ebay_api' => $this->circuitBreaker->getMetrics('ebay_api')
            ],
            'api_health' => $this->apiHealth->getSystemHealth(),
            'database' => $this->dbOptimization->getOptimizationSummary(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get real-time metrics (for auto-refresh)
     */
    public function realtime()
    {
        return response()->json([
            'queue_summary' => $this->queueHealth->getQueueHealthSummary(),
            'rate_limits' => [
                'eps' => $this->rateLimiter->getRateLimitStatus('eps'),
                'ebay' => $this->rateLimiter->getRateLimitStatus('ebay')
            ],
            'system_health' => $this->apiHealth->getSystemHealth(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get detailed queue monitoring
     */
    public function queueDetails()
    {
        $monitoringResult = $this->queueHealth->monitorQueues();
        
        return response()->json($monitoringResult);
    }

    /**
     * Force circuit breaker state (for testing/emergency)
     */
    public function circuitBreaker(Request $request)
    {
        $request->validate([
            'service' => 'required|in:eps_api,ebay_api',
            'state' => 'required|in:closed,open,half_open'
        ]);

        $this->circuitBreaker->forceState(
            $request->service,
            $request->state
        );

        return response()->json([
            'success' => true,
            'message' => "Circuit breaker for {$request->service} set to {$request->state}"
        ]);
    }

    /**
     * Trigger database optimization
     */
    public function optimizeDatabase()
    {
        $results = $this->dbOptimization->optimizeDatabase();
        
        return response()->json([
            'success' => true,
            'results' => $results
        ]);
    }

    /**
     * Get system metrics for alerting
     */
    public function alerts()
    {
        $queueHealth = $this->queueHealth->getQueueHealthSummary();
        $systemHealth = $this->apiHealth->getSystemHealth();
        
        $alerts = [];
        
        // Queue alerts
        if ($queueHealth['overall_health'] !== 'healthy') {
            $alerts[] = [
                'type' => 'queue',
                'severity' => $queueHealth['overall_health'],
                'message' => "Queue health: {$queueHealth['overall_health']}",
                'details' => $queueHealth
            ];
        }
        
        // API health alerts
        foreach ($systemHealth as $api => $health) {
            if (!$health['healthy']) {
                $alerts[] = [
                    'type' => 'api',
                    'severity' => 'warning',
                    'message' => "API {$api} is unhealthy",
                    'details' => $health
                ];
            }
        }
        
        return response()->json([
            'alerts' => $alerts,
            'alert_count' => count($alerts),
            'timestamp' => now()->toISOString()
        ]);
    }
} 