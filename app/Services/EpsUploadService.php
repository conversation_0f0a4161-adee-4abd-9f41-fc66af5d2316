<?php

namespace App\Services;

use App\Models\EpsImageCache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Services\RateLimitingService;
use App\Services\CircuitBreakerService;

class EpsUploadService
{
    private string $epsEndpoint;
    private string $apiKey;
    private string $apiSecret;

    public function __construct(
        private RateLimitingService $rateLimiter,
        private CircuitBreakerService $circuitBreaker
    ) {
        $this->epsEndpoint = config('ebay.eps_endpoint');
        $this->apiKey = config('ebay.api_key');
        $this->apiSecret = config('ebay.api_secret');
    }

    /**
     * Upload an image to eBay EPS with resilience patterns
     */
    public function uploadImage(
        string $imageUrl,
        string $shopifyImageId,
        int $shopifyProductId,
        ?int $shopifyVariantId = null,
        ?string $variationSpecificValue = null
    ): ?string {
        try {
            // Check cache first
            $cached = $this->getCachedEpsUrl($shopifyImageId);
            if ($cached) {
                Log::info('Using cached EPS URL', [
                    'shopify_image_id' => $shopifyImageId,
                    'eps_url' => $cached
                ]);
                return $cached;
            }

            // Check circuit breaker
            if (!$this->circuitBreaker->canExecute('eps_api')) {
                Log::warning('EPS upload blocked by circuit breaker', [
                    'shopify_image_id' => $shopifyImageId
                ]);
                return null;
            }

            // Check rate limiting
            if (!$this->rateLimiter->canMakeEpsRequest()) {
                $waitTime = $this->rateLimiter->getWaitTimeForEps();
                Log::warning('EPS upload rate limited', [
                    'shopify_image_id' => $shopifyImageId,
                    'wait_time' => $waitTime
                ]);
                
                // Re-queue with delay instead of failing
                $this->requeueWithDelay($shopifyImageId, $shopifyProductId, $waitTime);
                return null;
            }

            // Upload to EPS with retry logic
            $epsResponse = $this->callEpsApiWithRetry($imageUrl);
            
            if ($epsResponse && $epsResponse['success']) {
                $this->circuitBreaker->recordSuccess('eps_api');
                
                // Cache the result
                $this->cacheEpsResult(
                    $shopifyImageId,
                    $shopifyProductId,
                    $shopifyVariantId,
                    $variationSpecificValue,
                    $imageUrl,
                    $epsResponse
                );

                return $epsResponse['eps_url'];
            }

            $this->circuitBreaker->recordFailure('eps_api', $epsResponse['error'] ?? 'Unknown error');
            
            Log::error('EPS upload failed', [
                'shopify_image_id' => $shopifyImageId,
                'error' => $epsResponse['error'] ?? 'Unknown error'
            ]);

            return null;

        } catch (Exception $e) {
            $this->circuitBreaker->recordFailure('eps_api', $e->getMessage());
            
            Log::error('EPS upload exception', [
                'shopify_image_id' => $shopifyImageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get cached EPS URL if valid
     */
    private function getCachedEpsUrl(string $shopifyImageId): ?string
    {
        $cached = EpsImageCache::byShopifyImageId($shopifyImageId)
            ->valid()
            ->first();

        if ($cached) {
            $cached->markAsUsed();
            return $cached->eps_url;
        }

        return null;
    }

    /**
     * Call eBay EPS API
     */
    private function callEpsApi(string $imageUrl): array
    {
        $payload = [
            'ExternalPictureURL' => $imageUrl,
            'PictureSet' => 'Standard',
            'UploadPolicy' => 'Add'
        ];

        $xmlBody = $this->buildEpsXml($payload);

        $response = Http::withHeaders([
            'X-EBAY-API-COMPATIBILITY-LEVEL' => '967',
            'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
            'X-EBAY-API-APP-NAME' => $this->apiKey,
            'X-EBAY-API-CERT-NAME' => $this->apiSecret,
            'X-EBAY-API-CALL-NAME' => 'UploadSiteHostedPictures',
            'X-EBAY-API-SITEID' => '0',
            'Content-Type' => 'text/xml'
        ])->timeout(30)->withBody($xmlBody, 'text/xml')->post($this->epsEndpoint);

        if ($response->successful()) {
            return $this->parseEpsResponse($response->body());
        }

        return [
            'success' => false,
            'error' => 'HTTP ' . $response->status() . ': ' . $response->body()
        ];
    }

    /**
     * Build EPS XML payload
     */
    private function buildEpsXml(array $data): string
    {
        return '<?xml version="1.0" encoding="utf-8"?>
            <UploadSiteHostedPicturesRequest xmlns="urn:ebay:apis:eBLBaseComponents">
                <RequesterCredentials>
                    <eBayAuthToken>' . config('ebay.auth_token') . '</eBayAuthToken>
                </RequesterCredentials>
                <ExternalPictureURL>' . htmlspecialchars($data['ExternalPictureURL']) . '</ExternalPictureURL>
                <PictureSet>' . $data['PictureSet'] . '</PictureSet>
                <UploadPolicy>' . $data['UploadPolicy'] . '</UploadPolicy>
            </UploadSiteHostedPicturesRequest>';
    }

    /**
     * Parse EPS API response
     */
    private function parseEpsResponse(string $xmlResponse): array
    {
        $xml = simplexml_load_string($xmlResponse);
        
        if (!$xml) {
            return ['success' => false, 'error' => 'Invalid XML response'];
        }

        $ack = (string) $xml->Ack;
        
        if ($ack === 'Success' || $ack === 'Warning') {
            return [
                'success' => true,
                'eps_url' => (string) $xml->SiteHostedPictureDetails->FullURL,
                'picture_name' => (string) $xml->SiteHostedPictureDetails->PictureName,
                'picture_format' => (string) $xml->SiteHostedPictureDetails->PictureFormat,
                'picture_set' => (string) $xml->SiteHostedPictureDetails->PictureSet,
                'use_by_date' => (string) $xml->SiteHostedPictureDetails->UseByDate,
                'ack' => $ack,
                'response' => $xmlResponse
            ];
        }

        $errors = [];
        if (isset($xml->Errors)) {
            foreach ($xml->Errors as $error) {
                $errors[] = (string) $error->LongMessage;
            }
        }

        return [
            'success' => false,
            'error' => implode('; ', $errors),
            'ack' => $ack,
            'response' => $xmlResponse
        ];
    }

    /**
     * Cache EPS upload result
     */
    private function cacheEpsResult(
        string $shopifyImageId,
        int $shopifyProductId,
        ?int $shopifyVariantId,
        ?string $variationSpecificValue,
        string $originalUrl,
        array $epsResponse
    ): void {
        EpsImageCache::updateOrCreate(
            ['shopify_image_id' => $shopifyImageId],
            [
                'shopify_product_id' => $shopifyProductId,
                'shopify_variant_id' => $shopifyVariantId,
                'variation_specific_value' => $variationSpecificValue,
                'original_url' => $originalUrl,
                'eps_url' => $epsResponse['eps_url'],
                'eps_uploaded_at' => now(),
                'use_by_date' => $epsResponse['use_by_date'] ? now()->parse($epsResponse['use_by_date']) : null,
                'picture_name' => $epsResponse['picture_name'] ?? null,
                'picture_format' => $epsResponse['picture_format'] ?? null,
                'picture_set' => $epsResponse['picture_set'] ?? 'Standard',
                'eps_ack' => $epsResponse['ack'],
                'eps_response' => $epsResponse['response'],
                'status' => 'valid'
            ]
        );
    }

    /**
     * Call EPS API with retry logic and exponential backoff
     */
    private function callEpsApiWithRetry(string $imageUrl, int $attempt = 1): array
    {
        $maxAttempts = 3;
        $baseDelay = 1; // Base delay in seconds
        
        try {
            $result = $this->callEpsApi($imageUrl);
            
            if ($result['success']) {
                $this->rateLimiter->recordEpsRequest();
                return $result;
            }
            
            // If it's a rate limit error and we have attempts left, retry
            if ($attempt < $maxAttempts && $this->isRateLimitError($result)) {
                $delay = $baseDelay * pow(2, $attempt - 1) + random_int(0, 1000) / 1000; // Add jitter
                
                Log::info('Retrying EPS API call after rate limit', [
                    'attempt' => $attempt,
                    'delay' => $delay,
                    'error' => $result['error']
                ]);
                
                sleep($delay);
                return $this->callEpsApiWithRetry($imageUrl, $attempt + 1);
            }
            
            return $result;
            
        } catch (Exception $e) {
            if ($attempt < $maxAttempts) {
                $delay = $baseDelay * pow(2, $attempt - 1) + random_int(0, 1000) / 1000;
                
                Log::warning('Retrying EPS API call after exception', [
                    'attempt' => $attempt,
                    'delay' => $delay,
                    'error' => $e->getMessage()
                ]);
                
                sleep($delay);
                return $this->callEpsApiWithRetry($imageUrl, $attempt + 1);
            }
            
            throw $e;
        }
    }

    /**
     * Check if error is rate limit related
     */
    private function isRateLimitError(array $result): bool
    {
        $error = strtolower($result['error'] ?? '');
        return strpos($error, 'rate limit') !== false ||
               strpos($error, 'too many requests') !== false ||
               strpos($error, '429') !== false;
    }

    /**
     * Re-queue image upload with delay
     */
    private function requeueWithDelay(string $shopifyImageId, int $shopifyProductId, int $delaySeconds): void
    {
        // This would dispatch the job again with delay
        // Implementation depends on your job dispatching strategy
        Log::info('Re-queuing EPS upload with delay', [
            'shopify_image_id' => $shopifyImageId,
            'delay_seconds' => $delaySeconds
        ]);
    }
} 