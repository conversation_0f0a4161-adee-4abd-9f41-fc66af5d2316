<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\EpsImageCache;

class DatabaseOptimizationService
{
    private const OPTIMIZATION_CACHE_TTL = 3600; // 1 hour

    /**
     * Run comprehensive database optimization
     */
    public function optimizeDatabase(): array
    {
        $results = [];
        
        $results['cache_cleanup'] = $this->cleanupExpiredCache();
        $results['index_optimization'] = $this->optimizeIndexes();
        $results['query_optimization'] = $this->analyzeSlowQueries();
        $results['table_maintenance'] = $this->performTableMaintenance();
        $results['connection_optimization'] = $this->optimizeConnections();
        
        // Cache optimization results
        Cache::put('db_optimization_results', $results, self::OPTIMIZATION_CACHE_TTL);
        
        return $results;
    }

    /**
     * Cleanup expired cache entries
     */
    private function cleanupExpiredCache(): array
    {
        try {
            $beforeCount = EpsImageCache::count();
            
            // Delete expired entries
            $deletedExpired = EpsImageCache::where('expires_at', '<', now())
                ->orWhere('status', 'expired')
                ->delete();
            
            // Delete old failed entries (older than 7 days)
            $deletedFailed = EpsImageCache::where('eps_ack', 'Failure')
                ->where('eps_uploaded_at', '<', now()->subDays(7))
                ->delete();
            
            // Delete unused entries (not accessed in 30 days and usage_count = 0)
            $deletedUnused = EpsImageCache::where('usage_count', 0)
                ->where('last_used_at', '<', now()->subDays(30))
                ->orWhereNull('last_used_at')
                ->where('eps_uploaded_at', '<', now()->subDays(30))
                ->delete();
            
            $afterCount = EpsImageCache::count();
            
            Log::info('EPS cache cleanup completed', [
                'before_count' => $beforeCount,
                'after_count' => $afterCount,
                'deleted_expired' => $deletedExpired,
                'deleted_failed' => $deletedFailed,
                'deleted_unused' => $deletedUnused,
                'total_deleted' => $beforeCount - $afterCount
            ]);
            
            return [
                'success' => true,
                'before_count' => $beforeCount,
                'after_count' => $afterCount,
                'deleted_expired' => $deletedExpired,
                'deleted_failed' => $deletedFailed,
                'deleted_unused' => $deletedUnused,
                'space_saved_estimated_mb' => ($beforeCount - $afterCount) * 0.002 // Rough estimate
            ];
            
        } catch (\Exception $e) {
            Log::error('Cache cleanup failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Optimize database indexes
     */
    private function optimizeIndexes(): array
    {
        try {
            $results = [];
            
            // Analyze eps_image_cache table indexes
            $cacheIndexAnalysis = $this->analyzeTableIndexes('eps_image_cache');
            $results['eps_image_cache'] = $cacheIndexAnalysis;
            
            // Analyze jobs table indexes  
            $jobsIndexAnalysis = $this->analyzeTableIndexes('jobs');
            $results['jobs'] = $jobsIndexAnalysis;
            
            // Check for missing indexes
            $missingIndexes = $this->findMissingIndexes();
            $results['missing_indexes'] = $missingIndexes;
            
            return [
                'success' => true,
                'analysis' => $results,
                'recommendations' => $this->generateIndexRecommendations($results)
            ];
            
        } catch (\Exception $e) {
            Log::error('Index optimization failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Analyze table indexes
     */
    private function analyzeTableIndexes(string $tableName): array
    {
        try {
            $indexes = DB::select("SHOW INDEXES FROM {$tableName}");
            $stats = DB::select("SHOW TABLE STATUS LIKE '{$tableName}'");
            
            return [
                'indexes' => $indexes,
                'table_stats' => $stats[0] ?? null,
                'index_usage' => $this->getIndexUsageStats($tableName)
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get index usage statistics
     */
    private function getIndexUsageStats(string $tableName): array
    {
        try {
            // This would require performance_schema to be enabled
            $query = "
                SELECT 
                    object_name,
                    index_name,
                    count_read,
                    count_write,
                    count_fetch,
                    count_insert,
                    count_update,
                    count_delete
                FROM performance_schema.table_io_waits_summary_by_index_usage 
                WHERE object_schema = DATABASE() 
                AND object_name = ?
            ";
            
            return DB::select($query, [$tableName]);
        } catch (\Exception $e) {
            // Performance schema might not be available
            return [];
        }
    }

    /**
     * Find missing indexes based on query patterns
     */
    private function findMissingIndexes(): array
    {
        $recommendations = [];
        
        // Common query patterns for eps_image_cache
        $commonQueries = [
            'shopify_product_id + status' => [
                'table' => 'eps_image_cache',
                'columns' => ['shopify_product_id', 'status'],
                'reason' => 'Frequently used for product image lookups'
            ],
            'expires_at + status' => [
                'table' => 'eps_image_cache',
                'columns' => ['expires_at', 'status'],
                'reason' => 'Used for cache expiry queries'
            ],
            'queue + reserved_at' => [
                'table' => 'jobs',
                'columns' => ['queue', 'reserved_at'],
                'reason' => 'Critical for queue job processing'
            ]
        ];
        
        foreach ($commonQueries as $name => $query) {
            if ($this->shouldCreateIndex($query['table'], $query['columns'])) {
                $recommendations[] = [
                    'name' => $name,
                    'table' => $query['table'],
                    'columns' => $query['columns'],
                    'reason' => $query['reason'],
                    'sql' => $this->generateIndexSQL($query['table'], $query['columns'], $name)
                ];
            }
        }
        
        return $recommendations;
    }

    /**
     * Check if index should be created
     */
    private function shouldCreateIndex(string $table, array $columns): bool
    {
        try {
            $indexes = DB::select("SHOW INDEXES FROM {$table}");
            
            // Check if composite index already exists
            foreach ($indexes as $index) {
                if ($index->Column_name === $columns[0]) {
                    // Simple check - could be more sophisticated
                    return false;
                }
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate index creation SQL
     */
    private function generateIndexSQL(string $table, array $columns, string $name): string
    {
        $indexName = 'idx_' . str_replace([' ', '+'], '_', strtolower($name));
        $columnList = implode(', ', $columns);
        
        return "CREATE INDEX {$indexName} ON {$table} ({$columnList})";
    }

    /**
     * Analyze slow queries
     */
    private function analyzeSlowQueries(): array
    {
        try {
            // Get slow query log status
            $slowLogStatus = DB::select("SHOW VARIABLES LIKE 'slow_query_log'");
            $slowLogFile = DB::select("SHOW VARIABLES LIKE 'slow_query_log_file'");
            $longQueryTime = DB::select("SHOW VARIABLES LIKE 'long_query_time'");
            
            // Get recent slow queries count
            $slowQueries = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
            
            return [
                'slow_log_enabled' => ($slowLogStatus[0]->Value ?? 'OFF') === 'ON',
                'slow_log_file' => $slowLogFile[0]->Value ?? 'unknown',
                'long_query_time' => $longQueryTime[0]->Value ?? 'unknown',
                'slow_queries_count' => $slowQueries[0]->Value ?? 0,
                'recommendations' => $this->getSlowQueryRecommendations()
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get slow query optimization recommendations
     */
    private function getSlowQueryRecommendations(): array
    {
        return [
            'Enable slow query logging if not enabled',
            'Set long_query_time to 1 second for development',
            'Monitor and optimize queries taking > 2 seconds',
            'Consider query caching for repeated expensive queries',
            'Review N+1 query patterns in application code'
        ];
    }

    /**
     * Perform table maintenance
     */
    private function performTableMaintenance(): array
    {
        try {
            $results = [];
            
            $tables = ['eps_image_cache', 'jobs', 'failed_jobs'];
            
            foreach ($tables as $table) {
                $results[$table] = $this->maintainTable($table);
            }
            
            return [
                'success' => true,
                'tables' => $results
            ];
            
        } catch (\Exception $e) {
            Log::error('Table maintenance failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Maintain individual table
     */
    private function maintainTable(string $tableName): array
    {
        try {
            // Analyze table
            DB::statement("ANALYZE TABLE {$tableName}");
            
            // Get table status before optimization
            $beforeStats = DB::select("SHOW TABLE STATUS LIKE '{$tableName}'")[0];
            
            // Optimize table
            DB::statement("OPTIMIZE TABLE {$tableName}");
            
            // Get table status after optimization
            $afterStats = DB::select("SHOW TABLE STATUS LIKE '{$tableName}'")[0];
            
            return [
                'before_size_mb' => round($beforeStats->Data_length / 1024 / 1024, 2),
                'after_size_mb' => round($afterStats->Data_length / 1024 / 1024, 2),
                'space_saved_mb' => round(($beforeStats->Data_length - $afterStats->Data_length) / 1024 / 1024, 2),
                'fragmentation_reduced' => $beforeStats->Data_free > $afterStats->Data_free
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Optimize database connections
     */
    private function optimizeConnections(): array
    {
        try {
            $variables = [
                'max_connections',
                'max_user_connections', 
                'thread_cache_size',
                'query_cache_size',
                'innodb_buffer_pool_size'
            ];
            
            $current = [];
            foreach ($variables as $var) {
                $result = DB::select("SHOW VARIABLES LIKE '{$var}'");
                $current[$var] = $result[0]->Value ?? 'unknown';
            }
            
            $status = [
                'Threads_connected',
                'Threads_running',
                'Max_used_connections',
                'Connections'
            ];
            
            $stats = [];
            foreach ($status as $stat) {
                $result = DB::select("SHOW GLOBAL STATUS LIKE '{$stat}'");
                $stats[$stat] = $result[0]->Value ?? 'unknown';
            }
            
            return [
                'current_settings' => $current,
                'connection_stats' => $stats,
                'recommendations' => $this->getConnectionRecommendations($current, $stats)
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Generate connection optimization recommendations
     */
    private function getConnectionRecommendations(array $settings, array $stats): array
    {
        $recommendations = [];
        
        $maxConnections = intval($settings['max_connections'] ?? 0);
        $maxUsed = intval($stats['Max_used_connections'] ?? 0);
        
        if ($maxUsed > $maxConnections * 0.8) {
            $recommendations[] = 'Consider increasing max_connections (currently using ' . 
                                round(($maxUsed / $maxConnections) * 100) . '% of capacity)';
        }
        
        $threadsConnected = intval($stats['Threads_connected'] ?? 0);
        $threadsRunning = intval($stats['Threads_running'] ?? 0);
        
        if ($threadsRunning > $threadsConnected * 0.1) {
            $recommendations[] = 'High thread activity detected - consider connection pooling';
        }
        
        return $recommendations;
    }

    /**
     * Generate index recommendations
     */
    private function generateIndexRecommendations(array $analysis): array
    {
        $recommendations = [];
        
        foreach ($analysis as $table => $data) {
            if (isset($data['error'])) {
                continue;
            }
            
            // Check table size vs index size
            $tableStats = $data['table_stats'] ?? null;
            if ($tableStats) {
                $dataSize = $tableStats->Data_length;
                $indexSize = $tableStats->Index_length;
                
                if ($indexSize > $dataSize * 2) {
                    $recommendations[] = "Table {$table} has very large indexes relative to data - review index necessity";
                }
            }
        }
        
        return $recommendations;
    }

    /**
     * Get optimization summary
     */
    public function getOptimizationSummary(): array
    {
        $cached = Cache::get('db_optimization_results', []);
        
        if (empty($cached)) {
            return [
                'last_optimization' => 'never',
                'recommendation' => 'Run database optimization'
            ];
        }
        
        return [
            'last_optimization' => 'within last hour',
            'cache_cleanup' => $cached['cache_cleanup']['success'] ?? false,
            'indexes_analyzed' => isset($cached['index_optimization']['success']),
            'tables_maintained' => $cached['table_maintenance']['success'] ?? false,
            'space_saved_mb' => $cached['cache_cleanup']['space_saved_estimated_mb'] ?? 0
        ];
    }
} 