<?php

namespace App\Services;

use App\Models\EpsImageCache;
use App\Jobs\UploadImageToEps;
use Illuminate\Support\Collection;

class ImageResolutionService
{
    /**
     * Resolve images for eBay listing with immediate cache lookup and background processing
     */
    public function resolveImagesForListing(array $images, int $shopifyProductId, int $ebayItemId): array
    {
        $resolvedImages = [];
        $pendingUploads = [];

        foreach ($images as $image) {
            $shopifyImageId = (string) $image['id'];
            $imageUrl = $image['src'];
            $variantId = $image['variant_id'] ?? null;
            $variationValue = $image['variation_specific_value'] ?? null;

            // Check cache first
            $cached = EpsImageCache::byShopifyImageId($shopifyImageId)
                ->valid()
                ->first();

            if ($cached) {
                // Use cached EPS URL
                $cached->markAsUsed();
                $resolvedImages[] = [
                    'shopify_image_id' => $shopifyImageId,
                    'url' => $cached->eps_url,
                    'source' => 'eps',
                    'variation_specific_value' => $cached->variation_specific_value
                ];
            } else {
                // Use original URL and queue for EPS upload
                $resolvedImages[] = [
                    'shopify_image_id' => $shopifyImageId,
                    'url' => $imageUrl,
                    'source' => 'original',
                    'variation_specific_value' => $variationValue
                ];

                // Queue for background EPS upload
                UploadImageToEps::dispatch(
                    $imageUrl,
                    $shopifyImageId,
                    $shopifyProductId,
                    $ebayItemId,
                    $variantId,
                    $variationValue
                );
            }
        }

        return [
            'images' => $resolvedImages,
            'pending_uploads' => count($pendingUploads),
            'immediate_eps_count' => count(array_filter($resolvedImages, fn($img) => $img['source'] === 'eps'))
        ];
    }

    /**
     * Get all available EPS URLs for a product
     */
    public function getProductEpsImages(int $shopifyProductId): Collection
    {
        return EpsImageCache::byProductId($shopifyProductId)
            ->valid()
            ->get();
    }

    /**
     * Get cached EPS URL if valid
     */
    public function getCachedEpsUrl(string $shopifyImageId): ?string
    {
        $cached = EpsImageCache::byShopifyImageId($shopifyImageId)
            ->valid()
            ->first();

        return $cached?->eps_url;
    }

    /**
     * Resolve images with variation mapping for eBay listing
     */
    public function resolveImagesWithVariations(
        array $productImages,
        array $variantData,
        int $shopifyProductId,
        int $ebayItemId
    ): array {
        $mainImages = $this->resolveMainImages($productImages, $shopifyProductId, $ebayItemId);
        $variationImages = $this->resolveVariationImages($variantData, $shopifyProductId, $ebayItemId);

        return [
            'main_images' => $mainImages,
            'variation_images' => $variationImages,
            'has_variations' => !empty($variationImages),
            'total_pending_uploads' => $mainImages['pending_uploads'] + $variationImages['pending_uploads']
        ];
    }

    /**
     * Resolve main product images
     */
    private function resolveMainImages(array $images, int $shopifyProductId, int $ebayItemId): array
    {
        return $this->resolveImagesForListing($images, $shopifyProductId, $ebayItemId);
    }

    /**
     * Resolve variation-specific images
     */
    private function resolveVariationImages(array $variantData, int $shopifyProductId, int $ebayItemId): array
    {
        $resolvedVariations = [];
        $totalPendingUploads = 0;

        foreach ($variantData as $variant) {
            if (!empty($variant['images'])) {
                $variationValue = $this->extractVariationValue($variant);
                
                foreach ($variant['images'] as $image) {
                    $image['variation_specific_value'] = $variationValue;
                    
                    $resolved = $this->resolveImagesForListing(
                        [$image],
                        $shopifyProductId,
                        $ebayItemId
                    );
                    
                    $resolvedVariations[$variationValue] = [
                        'images' => $resolved['images'],
                        'variation_attribute' => $variant['attribute_name'] ?? 'Color'
                    ];
                    
                    $totalPendingUploads += $resolved['pending_uploads'];
                }
            }
        }

        return [
            'variations' => $resolvedVariations,
            'pending_uploads' => $totalPendingUploads
        ];
    }

    /**
     * Extract variation value from variant data
     */
    private function extractVariationValue(array $variant): string
    {
        // Extract the variation value (e.g., 'Red', 'Large', etc.)
        // This depends on your Shopify variant data structure
        return $variant['option1'] ?? $variant['color'] ?? $variant['size'] ?? 'Default';
    }

    /**
     * Get variation attribute name from variants
     */
    public function getVariationAttributeName(array $variantData): ?string
    {
        if (empty($variantData)) {
            return null;
        }

        // Determine the primary variation attribute
        $firstVariant = reset($variantData);
        return $firstVariant['attribute_name'] ?? 'Color';
    }
} 