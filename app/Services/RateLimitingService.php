<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RateLimitingService
{
    private const EPS_RATE_LIMIT_KEY = 'eps_api_rate_limit';
    private const EBAY_API_RATE_LIMIT_KEY = 'ebay_api_rate_limit';
    
    // eBay EPS API limits (adjust based on your actual limits)
    private const EPS_REQUESTS_PER_HOUR = 5000;
    private const EPS_REQUESTS_PER_MINUTE = 100;
    private const EPS_BURST_CAPACITY = 20;
    
    // eBay Trading API limits
    private const EBAY_REQUESTS_PER_HOUR = 5000;
    private const EBAY_REQUESTS_PER_MINUTE = 100;

    /**
     * Check if EPS API request is allowed
     */
    public function canMakeEpsRequest(): bool
    {
        return $this->checkRateLimit(
            self::EPS_RATE_LIMIT_KEY,
            self::EPS_REQUESTS_PER_MINUTE,
            self::EPS_REQUESTS_PER_HOUR,
            self::EPS_BURST_CAPACITY
        );
    }

    /**
     * Check if eBay API request is allowed
     */
    public function canMakeEbayRequest(): bool
    {
        return $this->checkRateLimit(
            self::EBAY_API_RATE_LIMIT_KEY,
            self::EBAY_REQUESTS_PER_MINUTE,
            self::EBAY_REQUESTS_PER_HOUR
        );
    }

    /**
     * Record EPS API request
     */
    public function recordEpsRequest(): void
    {
        $this->recordRequest(self::EPS_RATE_LIMIT_KEY);
    }

    /**
     * Record eBay API request
     */
    public function recordEbayRequest(): void
    {
        $this->recordRequest(self::EBAY_API_RATE_LIMIT_KEY);
    }

    /**
     * Get wait time until next request is allowed
     */
    public function getWaitTimeForEps(): int
    {
        return $this->getWaitTime(self::EPS_RATE_LIMIT_KEY, self::EPS_REQUESTS_PER_MINUTE);
    }

    /**
     * Get wait time until next eBay request is allowed
     */
    public function getWaitTimeForEbay(): int
    {
        return $this->getWaitTime(self::EBAY_API_RATE_LIMIT_KEY, self::EBAY_REQUESTS_PER_MINUTE);
    }

    /**
     * Token bucket rate limiting implementation
     */
    private function checkRateLimit(
        string $key,
        int $perMinuteLimit,
        int $perHourLimit,
        int $burstCapacity = null
    ): bool {
        $now = now();
        $minuteKey = $key . ':minute:' . $now->format('Y-m-d:H:i');
        $hourKey = $key . ':hour:' . $now->format('Y-m-d:H');
        $burstKey = $key . ':burst';

        // Check per-minute limit
        $minuteCount = Cache::get($minuteKey, 0);
        if ($minuteCount >= $perMinuteLimit) {
            Log::warning('Rate limit exceeded: per-minute', [
                'key' => $key,
                'count' => $minuteCount,
                'limit' => $perMinuteLimit
            ]);
            return false;
        }

        // Check per-hour limit
        $hourCount = Cache::get($hourKey, 0);
        if ($hourCount >= $perHourLimit) {
            Log::warning('Rate limit exceeded: per-hour', [
                'key' => $key,
                'count' => $hourCount,
                'limit' => $perHourLimit
            ]);
            return false;
        }

        // Check burst capacity if specified
        if ($burstCapacity !== null) {
            $burstData = Cache::get($burstKey, ['count' => $burstCapacity, 'last_refill' => $now->timestamp]);
            
            // Refill tokens based on time passed
            $timePassed = $now->timestamp - $burstData['last_refill'];
            $tokensToAdd = intval($timePassed / 3); // Add 1 token every 3 seconds
            $burstData['count'] = min($burstCapacity, $burstData['count'] + $tokensToAdd);
            $burstData['last_refill'] = $now->timestamp;
            
            if ($burstData['count'] <= 0) {
                Log::warning('Rate limit exceeded: burst capacity', [
                    'key' => $key,
                    'burst_tokens' => $burstData['count']
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Record a request in rate limiting counters
     */
    private function recordRequest(string $key): void
    {
        $now = now();
        $minuteKey = $key . ':minute:' . $now->format('Y-m-d:H:i');
        $hourKey = $key . ':hour:' . $now->format('Y-m-d:H');
        $burstKey = $key . ':burst';

        // Increment counters
        Cache::increment($minuteKey);
        Cache::increment($hourKey);
        
        // Set expiry if new
        Cache::put($minuteKey, Cache::get($minuteKey), 70); // 70 seconds for minute counter
        Cache::put($hourKey, Cache::get($hourKey), 3700); // ~1 hour for hour counter
        
        // Decrease burst tokens
        $burstData = Cache::get($burstKey, ['count' => 20, 'last_refill' => $now->timestamp]);
        $burstData['count'] = max(0, $burstData['count'] - 1);
        Cache::put($burstKey, $burstData, 300); // 5 minutes
    }

    /**
     * Calculate wait time until rate limit resets
     */
    private function getWaitTime(string $key, int $perMinuteLimit): int
    {
        $now = now();
        $minuteKey = $key . ':minute:' . $now->format('Y-m-d:H:i');
        $minuteCount = Cache::get($minuteKey, 0);
        
        if ($minuteCount >= $perMinuteLimit) {
            return 60 - $now->second; // Wait until next minute
        }
        
        return 0;
    }

    /**
     * Get current rate limit status
     */
    public function getRateLimitStatus(string $apiType = 'eps'): array
    {
        $key = $apiType === 'eps' ? self::EPS_RATE_LIMIT_KEY : self::EBAY_API_RATE_LIMIT_KEY;
        $now = now();
        
        $minuteKey = $key . ':minute:' . $now->format('Y-m-d:H:i');
        $hourKey = $key . ':hour:' . $now->format('Y-m-d:H');
        $burstKey = $key . ':burst';
        
        return [
            'minute_count' => Cache::get($minuteKey, 0),
            'minute_limit' => $apiType === 'eps' ? self::EPS_REQUESTS_PER_MINUTE : self::EBAY_REQUESTS_PER_MINUTE,
            'hour_count' => Cache::get($hourKey, 0),
            'hour_limit' => $apiType === 'eps' ? self::EPS_REQUESTS_PER_HOUR : self::EBAY_REQUESTS_PER_HOUR,
            'burst_tokens' => Cache::get($burstKey, ['count' => 20])['count'] ?? 20,
            'wait_time' => $this->getWaitTime($key, $apiType === 'eps' ? self::EPS_REQUESTS_PER_MINUTE : self::EBAY_REQUESTS_PER_MINUTE)
        ];
    }
} 