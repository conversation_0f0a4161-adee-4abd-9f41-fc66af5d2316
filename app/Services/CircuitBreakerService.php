<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CircuitBreakerService
{
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';
    
    private const FAILURE_THRESHOLD = 5;
    private const RECOVERY_TIMEOUT = 300; // 5 minutes
    private const SUCCESS_THRESHOLD = 3; // Required successes to close circuit

    /**
     * Check if circuit breaker allows request
     */
    public function canExecute(string $service): bool
    {
        $state = $this->getCircuitState($service);
        
        switch ($state['state']) {
            case self::STATE_CLOSED:
                return true;
                
            case self::STATE_OPEN:
                if ($this->shouldAttemptReset($state)) {
                    $this->setState($service, self::STATE_HALF_OPEN);
                    return true;
                }
                return false;
                
            case self::STATE_HALF_OPEN:
                return true;
                
            default:
                return true;
        }
    }

    /**
     * Record successful execution
     */
    public function recordSuccess(string $service): void
    {
        $state = $this->getCircuitState($service);
        
        if ($state['state'] === self::STATE_HALF_OPEN) {
            $successes = $state['consecutive_successes'] + 1;
            
            if ($successes >= self::SUCCESS_THRESHOLD) {
                $this->setState($service, self::STATE_CLOSED);
                Log::info('Circuit breaker closed after successful recovery', [
                    'service' => $service,
                    'consecutive_successes' => $successes
                ]);
            } else {
                $this->updateSuccessCount($service, $successes);
            }
        } else {
            // Reset failure count on success
            $this->resetFailureCount($service);
        }
    }

    /**
     * Record failed execution
     */
    public function recordFailure(string $service, string $error = null): void
    {
        $state = $this->getCircuitState($service);
        $failures = $state['failure_count'] + 1;
        
        Log::warning('Circuit breaker recorded failure', [
            'service' => $service,
            'failure_count' => $failures,
            'error' => $error
        ]);
        
        if ($failures >= self::FAILURE_THRESHOLD) {
            $this->setState($service, self::STATE_OPEN);
            Log::error('Circuit breaker opened due to failures', [
                'service' => $service,
                'failure_count' => $failures,
                'threshold' => self::FAILURE_THRESHOLD
            ]);
        } else {
            $this->updateFailureCount($service, $failures);
        }
    }

    /**
     * Get current circuit state
     */
    public function getCircuitState(string $service): array
    {
        $key = $this->getStateKey($service);
        
        return Cache::get($key, [
            'state' => self::STATE_CLOSED,
            'failure_count' => 0,
            'consecutive_successes' => 0,
            'last_failure_time' => null,
            'opened_at' => null
        ]);
    }

    /**
     * Force circuit state (for testing/manual intervention)
     */
    public function forceState(string $service, string $state): void
    {
        $this->setState($service, $state);
        
        Log::info('Circuit breaker state manually set', [
            'service' => $service,
            'state' => $state
        ]);
    }

    /**
     * Get circuit breaker metrics
     */
    public function getMetrics(string $service): array
    {
        $state = $this->getCircuitState($service);
        
        return [
            'service' => $service,
            'state' => $state['state'],
            'failure_count' => $state['failure_count'],
            'consecutive_successes' => $state['consecutive_successes'],
            'last_failure_time' => $state['last_failure_time'],
            'opened_at' => $state['opened_at'],
            'failure_threshold' => self::FAILURE_THRESHOLD,
            'recovery_timeout' => self::RECOVERY_TIMEOUT,
            'success_threshold' => self::SUCCESS_THRESHOLD,
            'is_available' => $this->canExecute($service)
        ];
    }

    /**
     * Set circuit state
     */
    private function setState(string $service, string $state): void
    {
        $key = $this->getStateKey($service);
        $currentState = $this->getCircuitState($service);
        
        $newState = array_merge($currentState, [
            'state' => $state,
            'opened_at' => $state === self::STATE_OPEN ? now()->timestamp : $currentState['opened_at']
        ]);
        
        if ($state === self::STATE_CLOSED) {
            $newState['failure_count'] = 0;
            $newState['consecutive_successes'] = 0;
            $newState['opened_at'] = null;
        }
        
        Cache::put($key, $newState, 3600); // 1 hour TTL
    }

    /**
     * Update failure count
     */
    private function updateFailureCount(string $service, int $count): void
    {
        $key = $this->getStateKey($service);
        $state = $this->getCircuitState($service);
        
        $state['failure_count'] = $count;
        $state['last_failure_time'] = now()->timestamp;
        $state['consecutive_successes'] = 0;
        
        Cache::put($key, $state, 3600);
    }

    /**
     * Update success count
     */
    private function updateSuccessCount(string $service, int $count): void
    {
        $key = $this->getStateKey($service);
        $state = $this->getCircuitState($service);
        
        $state['consecutive_successes'] = $count;
        
        Cache::put($key, $state, 3600);
    }

    /**
     * Reset failure count
     */
    private function resetFailureCount(string $service): void
    {
        $key = $this->getStateKey($service);
        $state = $this->getCircuitState($service);
        
        $state['failure_count'] = 0;
        $state['consecutive_successes'] = 0;
        
        Cache::put($key, $state, 3600);
    }

    /**
     * Check if circuit should attempt reset
     */
    private function shouldAttemptReset(array $state): bool
    {
        if (!$state['opened_at']) {
            return true;
        }
        
        $timeSinceOpened = now()->timestamp - $state['opened_at'];
        return $timeSinceOpened >= self::RECOVERY_TIMEOUT;
    }

    /**
     * Get cache key for service state
     */
    private function getStateKey(string $service): string
    {
        return "circuit_breaker:{$service}";
    }
} 