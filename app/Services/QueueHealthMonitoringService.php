<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class QueueHealthMonitoringService
{
    private const HEALTH_CHECK_INTERVAL = 60; // 1 minute
    private const QUEUE_THRESHOLDS = [
        'eps-upload-high' => ['warning' => 100, 'critical' => 500],
        'eps-upload-medium' => ['warning' => 200, 'critical' => 1000],
        'listing-revision' => ['warning' => 50, 'critical' => 200],
        'eps-failed' => ['warning' => 10, 'critical' => 50]
    ];

    /**
     * Monitor all queues and trigger alerts/scaling
     */
    public function monitorQueues(): array
    {
        $queueStatus = [];
        $alerts = [];
        $scalingActions = [];

        foreach (self::QUEUE_THRESHOLDS as $queueName => $thresholds) {
            $status = $this->checkQueueHealth($queueName, $thresholds);
            $queueStatus[$queueName] = $status;

            if ($status['status'] !== 'healthy') {
                $alerts[] = $this->createAlert($queueName, $status);
                
                if ($status['status'] === 'critical') {
                    $scalingActions[] = $this->createScalingAction($queueName, $status);
                }
            }
        }

        // Execute scaling actions
        foreach ($scalingActions as $action) {
            $this->executeScalingAction($action);
        }

        // Store monitoring results
        Cache::put('queue_health_status', [
            'timestamp' => now()->toISOString(),
            'queues' => $queueStatus,
            'alerts' => $alerts,
            'scaling_actions' => $scalingActions
        ], 300);

        return [
            'queues' => $queueStatus,
            'alerts' => $alerts,
            'scaling_actions' => $scalingActions
        ];
    }

    /**
     * Check individual queue health
     */
    private function checkQueueHealth(string $queueName, array $thresholds): array
    {
        $pendingJobs = $this->getQueueSize($queueName);
        $processingRate = $this->getProcessingRate($queueName);
        $averageWaitTime = $this->getAverageWaitTime($queueName);
        $oldestJob = $this->getOldestJobAge($queueName);

        $status = 'healthy';
        $issues = [];

        // Check pending jobs threshold
        if ($pendingJobs >= $thresholds['critical']) {
            $status = 'critical';
            $issues[] = "Critical queue backlog: {$pendingJobs} pending jobs";
        } elseif ($pendingJobs >= $thresholds['warning']) {
            $status = 'warning';
            $issues[] = "Queue backlog warning: {$pendingJobs} pending jobs";
        }

        // Check processing rate
        if ($processingRate < 0.5 && $pendingJobs > 0) { // Less than 0.5 jobs per minute
            $status = $status === 'critical' ? 'critical' : 'warning';
            $issues[] = "Low processing rate: {$processingRate} jobs/min";
        }

        // Check job age
        if ($oldestJob > 3600) { // 1 hour
            $status = 'critical';
            $issues[] = "Jobs stuck for " . round($oldestJob / 60) . " minutes";
        } elseif ($oldestJob > 1800) { // 30 minutes
            $status = $status === 'critical' ? 'critical' : 'warning';
            $issues[] = "Jobs delayed for " . round($oldestJob / 60) . " minutes";
        }

        return [
            'status' => $status,
            'pending_jobs' => $pendingJobs,
            'processing_rate' => $processingRate,
            'average_wait_time' => $averageWaitTime,
            'oldest_job_age' => $oldestJob,
            'issues' => $issues
        ];
    }

    /**
     * Get queue size
     */
    private function getQueueSize(string $queueName): int
    {
        try {
            return DB::table('jobs')
                ->where('queue', $queueName)
                ->where('reserved_at', null)
                ->count();
        } catch (\Exception $e) {
            Log::error('Failed to get queue size', ['queue' => $queueName, 'error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get processing rate (jobs per minute)
     */
    private function getProcessingRate(string $queueName): float
    {
        $cacheKey = "processing_rate:{$queueName}";
        $now = now()->timestamp;
        
        // Get historical processing data
        $history = Cache::get($cacheKey, []);
        
        // Clean old data (older than 10 minutes)
        $history = array_filter($history, fn($timestamp) => $now - $timestamp < 600);
        
        // Calculate rate based on last 5 minutes
        $recentJobs = array_filter($history, fn($timestamp) => $now - $timestamp < 300);
        
        return count($recentJobs) / 5; // jobs per minute
    }

    /**
     * Record job processing for rate calculation
     */
    public function recordJobProcessed(string $queueName): void
    {
        $cacheKey = "processing_rate:{$queueName}";
        $history = Cache::get($cacheKey, []);
        
        $history[] = now()->timestamp;
        
        // Keep only last 10 minutes of data
        $cutoff = now()->timestamp - 600;
        $history = array_filter($history, fn($timestamp) => $timestamp > $cutoff);
        
        Cache::put($cacheKey, $history, 900); // 15 minutes TTL
    }

    /**
     * Get average wait time for jobs
     */
    private function getAverageWaitTime(string $queueName): float
    {
        try {
            $jobs = DB::table('jobs')
                ->where('queue', $queueName)
                ->where('reserved_at', null)
                ->select('created_at')
                ->limit(100)
                ->get();

            if ($jobs->isEmpty()) {
                return 0;
            }

            $totalWaitTime = 0;
            $now = now();

            foreach ($jobs as $job) {
                $waitTime = $now->diffInSeconds($job->created_at);
                $totalWaitTime += $waitTime;
            }

            return $totalWaitTime / $jobs->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get oldest job age in seconds
     */
    private function getOldestJobAge(string $queueName): int
    {
        try {
            $oldestJob = DB::table('jobs')
                ->where('queue', $queueName)
                ->where('reserved_at', null)
                ->orderBy('created_at')
                ->first();

            if (!$oldestJob) {
                return 0;
            }

            return now()->diffInSeconds($oldestJob->created_at);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Create alert for queue issues
     */
    private function createAlert(string $queueName, array $status): array
    {
        return [
            'queue' => $queueName,
            'severity' => $status['status'],
            'timestamp' => now()->toISOString(),
            'pending_jobs' => $status['pending_jobs'],
            'issues' => $status['issues'],
            'recommended_action' => $this->getRecommendedAction($queueName, $status)
        ];
    }

    /**
     * Get recommended action for queue issues
     */
    private function getRecommendedAction(string $queueName, array $status): string
    {
        if ($status['oldest_job_age'] > 3600) {
            return 'Restart workers and check for stuck jobs';
        }
        
        if ($status['pending_jobs'] > 500) {
            return 'Scale up workers immediately';
        }
        
        if ($status['processing_rate'] < 0.5) {
            return 'Check worker health and API connectivity';
        }
        
        return 'Monitor closely and consider scaling';
    }

    /**
     * Create scaling action
     */
    private function createScalingAction(string $queueName, array $status): array
    {
        $currentWorkers = $this->getWorkerCount($queueName);
        $recommendedWorkers = $this->calculateRecommendedWorkers($queueName, $status);
        
        return [
            'queue' => $queueName,
            'action' => 'scale_up',
            'current_workers' => $currentWorkers,
            'recommended_workers' => $recommendedWorkers,
            'scale_factor' => $recommendedWorkers / max(1, $currentWorkers),
            'reason' => implode(', ', $status['issues'])
        ];
    }

    /**
     * Get current worker count for queue
     */
    private function getWorkerCount(string $queueName): int
    {
        // This would integrate with your worker management system
        // For now, return estimated count based on processing rate
        return max(1, intval(Cache::get("worker_count:{$queueName}", 1)));
    }

    /**
     * Calculate recommended worker count
     */
    private function calculateRecommendedWorkers(string $queueName, array $status): int
    {
        $pendingJobs = $status['pending_jobs'];
        $processingRate = max(0.1, $status['processing_rate']);
        
        // Target: clear backlog in 30 minutes
        $targetClearTime = 30; // minutes
        $jobsPerWorkerPerMinute = $processingRate;
        
        $requiredWorkers = ceil($pendingJobs / ($targetClearTime * $jobsPerWorkerPerMinute));
        
        // Cap at reasonable limits
        return min(20, max(1, $requiredWorkers));
    }

    /**
     * Execute scaling action
     */
    private function executeScalingAction(array $action): bool
    {
        try {
            Log::info('Executing queue scaling action', $action);
            
            // This would integrate with your infrastructure scaling system
            // For example, Kubernetes horizontal pod autoscaler, AWS ECS scaling, etc.
            
            // For demonstration, we'll just log and update cache
            Cache::put("worker_count:{$action['queue']}", $action['recommended_workers'], 3600);
            
            // You could trigger worker scaling here:
            // - Kubernetes: kubectl scale deployment
            // - Docker Swarm: docker service scale
            // - Supervisor: supervisorctl start/stop processes
            // - Laravel Horizon: programmatic scaling
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to execute scaling action', [
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get queue health summary
     */
    public function getQueueHealthSummary(): array
    {
        $status = Cache::get('queue_health_status', [
            'queues' => [],
            'alerts' => [],
            'scaling_actions' => []
        ]);

        $summary = [
            'overall_health' => 'healthy',
            'total_pending_jobs' => 0,
            'critical_queues' => 0,
            'warning_queues' => 0,
            'healthy_queues' => 0,
            'active_alerts' => count($status['alerts']),
            'recent_scaling_actions' => count($status['scaling_actions'])
        ];

        foreach ($status['queues'] as $queueStatus) {
            $summary['total_pending_jobs'] += $queueStatus['pending_jobs'];
            
            switch ($queueStatus['status']) {
                case 'critical':
                    $summary['critical_queues']++;
                    $summary['overall_health'] = 'critical';
                    break;
                case 'warning':
                    $summary['warning_queues']++;
                    if ($summary['overall_health'] === 'healthy') {
                        $summary['overall_health'] = 'warning';
                    }
                    break;
                case 'healthy':
                    $summary['healthy_queues']++;
                    break;
            }
        }

        return $summary;
    }
} 