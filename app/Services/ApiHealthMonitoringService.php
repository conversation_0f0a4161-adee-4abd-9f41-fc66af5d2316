<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ApiHealthMonitoringService
{
    private const HEALTH_CHECK_INTERVAL = 300; // 5 minutes
    private const HEALTH_HISTORY_DAYS = 7;

    /**
     * Record API response time and status
     */
    public function recordApiMetrics(string $api, bool $success, int $responseTime, ?string $error = null): void
    {
        $timestamp = now()->timestamp;
        $key = "api_metrics:{$api}:" . date('Y-m-d:H:i', $timestamp);
        
        $metrics = Cache::get($key, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'total_response_time' => 0,
            'errors' => []
        ]);
        
        $metrics['total_requests']++;
        $metrics['total_response_time'] += $responseTime;
        
        if ($success) {
            $metrics['successful_requests']++;
        } else {
            $metrics['failed_requests']++;
            if ($error) {
                $metrics['errors'][] = $error;
            }
        }
        
        Cache::put($key, $metrics, 3600); // 1 hour TTL
    }

    /**
     * Get API health metrics
     */
    public function getApiHealthMetrics(string $api, int $hours = 24): array
    {
        $endTime = now();
        $startTime = $endTime->copy()->subHours($hours);
        
        $totalRequests = 0;
        $successfulRequests = 0;
        $failedRequests = 0;
        $totalResponseTime = 0;
        $errors = [];
        
        for ($time = $startTime; $time <= $endTime; $time->addMinutes(5)) {
            $key = "api_metrics:{$api}:" . $time->format('Y-m-d:H:i');
            $metrics = Cache::get($key, []);
            
            if (!empty($metrics)) {
                $totalRequests += $metrics['total_requests'] ?? 0;
                $successfulRequests += $metrics['successful_requests'] ?? 0;
                $failedRequests += $metrics['failed_requests'] ?? 0;
                $totalResponseTime += $metrics['total_response_time'] ?? 0;
                $errors = array_merge($errors, $metrics['errors'] ?? []);
            }
        }
        
        return [
            'api' => $api,
            'period_hours' => $hours,
            'total_requests' => $totalRequests,
            'successful_requests' => $successfulRequests,
            'failed_requests' => $failedRequests,
            'success_rate' => $totalRequests > 0 ? ($successfulRequests / $totalRequests) * 100 : 0,
            'average_response_time' => $totalRequests > 0 ? $totalResponseTime / $totalRequests : 0,
            'error_count' => count($errors),
            'unique_errors' => array_unique($errors)
        ];
    }

    /**
     * Check if API is healthy
     */
    public function isApiHealthy(string $api): bool
    {
        $metrics = $this->getApiHealthMetrics($api, 1); // Last hour
        
        // Consider API healthy if:
        // - Success rate > 95%
        // - Average response time < 10 seconds
        // - Total requests > 0 (API is being used)
        
        return $metrics['success_rate'] >= 95 && 
               $metrics['average_response_time'] < 10000 && 
               $metrics['total_requests'] > 0;
    }

    /**
     * Get overall system health
     */
    public function getSystemHealth(): array
    {
        $apis = ['eps_api', 'ebay_api'];
        $health = [];
        
        foreach ($apis as $api) {
            $health[$api] = [
                'healthy' => $this->isApiHealthy($api),
                'metrics' => $this->getApiHealthMetrics($api, 1)
            ];
        }
        
        return $health;
    }
} 