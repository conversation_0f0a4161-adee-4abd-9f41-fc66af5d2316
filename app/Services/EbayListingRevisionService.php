<?php

namespace App\Services;

use App\Models\EpsImageCache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Exception;

class EbayListingRevisionService
{
    private string $apiEndpoint;
    private string $apiKey;
    private string $apiSecret;

    public function __construct(
        private RateLimitingService $rateLimiter,
        private CircuitBreakerService $circuitBreaker
    ) {
        $this->apiEndpoint = config('ebay.api_endpoint');
        $this->apiKey = config('ebay.api_key');
        $this->apiSecret = config('ebay.api_secret');
    }

    /**
     * Revise eBay listing with resilience patterns
     */
    public function reviseItemImages(int $ebayItemId, array $newEpsUrls = []): array
    {
        try {
            // Check circuit breaker
            if (!$this->circuitBreaker->canExecute('ebay_api')) {
                Log::warning('eBay revision blocked by circuit breaker', [
                    'ebay_item_id' => $ebayItemId
                ]);
                return [
                    'success' => false,
                    'error' => 'Service temporarily unavailable (circuit breaker open)'
                ];
            }

            // Check rate limiting
            if (!$this->rateLimiter->canMakeEbayRequest()) {
                $waitTime = $this->rateLimiter->getWaitTimeForEbay();
                Log::warning('eBay revision rate limited', [
                    'ebay_item_id' => $ebayItemId,
                    'wait_time' => $waitTime
                ]);
                
                return [
                    'success' => false,
                    'error' => "Rate limited, retry after {$waitTime} seconds",
                    'retry_after' => $waitTime
                ];
            }

            // Get all current EPS URLs for this listing
            $allImages = $this->getAllListingImages($ebayItemId);
            
            // Determine if this is a variation listing
            $hasVariations = $this->hasVariationImages($allImages);
            
            $result = $hasVariations 
                ? $this->reviseVariationListing($ebayItemId, $allImages)
                : $this->reviseSimpleListing($ebayItemId, $allImages);
            
            if ($result['success']) {
                $this->circuitBreaker->recordSuccess('ebay_api');
            } else {
                $this->circuitBreaker->recordFailure('ebay_api', $result['error'] ?? 'Unknown error');
            }
            
            return $result;

        } catch (Exception $e) {
            $this->circuitBreaker->recordFailure('ebay_api', $e->getMessage());
            
            Log::error('Failed to revise eBay listing', [
                'ebay_item_id' => $ebayItemId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all images for a listing from cache
     */
    private function getAllListingImages(int $ebayItemId): Collection
    {
        // We need to get the Shopify product ID from the eBay item ID
        // This assumes we have a mapping table or can derive it
        // For now, we'll get all valid EPS images for this revision
        return EpsImageCache::valid()
            ->whereNotNull('eps_url')
            ->get()
            ->groupBy('shopify_product_id');
    }

    /**
     * Check if listing has variation-specific images
     */
    private function hasVariationImages(Collection $images): bool
    {
        return $images->flatten()->whereNotNull('variation_specific_value')->isNotEmpty();
    }

    /**
     * Revise simple (non-variation) listing
     */
    private function reviseSimpleListing(int $ebayItemId, Collection $allImages): array
    {
        $imageUrls = $allImages->flatten()
            ->whereNull('variation_specific_value')
            ->pluck('eps_url')
            ->take(12) // eBay max images per listing
            ->values()
            ->toArray();

        if (empty($imageUrls)) {
            return ['success' => true, 'message' => 'No images to update'];
        }

        $xmlPayload = $this->buildSimpleListingXml($ebayItemId, $imageUrls);
        
        return $this->callReviseItemApi($xmlPayload);
    }

    /**
     * Revise variation listing with specific image mappings
     */
    private function reviseVariationListing(int $ebayItemId, Collection $allImages): array
    {
        $variationData = $this->buildVariationImageData($allImages);
        $xmlPayload = $this->buildVariationListingXml($ebayItemId, $variationData);
        
        return $this->callReviseItemApi($xmlPayload);
    }

    /**
     * Build variation image mapping data
     */
    private function buildVariationImageData(Collection $allImages): array
    {
        $variationImages = $allImages->flatten()
            ->whereNotNull('variation_specific_value')
            ->groupBy('variation_specific_value');

        $mainImages = $allImages->flatten()
            ->whereNull('variation_specific_value')
            ->pluck('eps_url')
            ->take(12)
            ->values()
            ->toArray();

        $variationSets = [];
        foreach ($variationImages as $variationValue => $images) {
            $variationSets[] = [
                'value' => $variationValue,
                'urls' => $images->pluck('eps_url')->take(1)->toArray() // One image per variation
            ];
        }

        return [
            'main_images' => $mainImages,
            'variation_sets' => $variationSets,
            'variation_name' => $this->getVariationAttributeName($allImages)
        ];
    }

    /**
     * Get variation attribute name (e.g., 'Color', 'Size')
     */
    private function getVariationAttributeName(Collection $allImages): string
    {
        // This would typically come from Shopify product data
        // For now, we'll default to 'Color' as it's most common
        return 'Color';
    }

    /**
     * Build XML for simple listing revision
     */
    private function buildSimpleListingXml(int $ebayItemId, array $imageUrls): string
    {
        $pictureDetailsXml = '';
        foreach ($imageUrls as $index => $url) {
            $pictureDetailsXml .= "<PictureURL>$url</PictureURL>\n";
        }

        return '<?xml version="1.0" encoding="utf-8"?>
            <ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
                <RequesterCredentials>
                    <eBayAuthToken>' . config('ebay.auth_token') . '</eBayAuthToken>
                </RequesterCredentials>
                <Item>
                    <ItemID>' . $ebayItemId . '</ItemID>
                    <PictureDetails>
                        ' . $pictureDetailsXml . '
                    </PictureDetails>
                </Item>
            </ReviseFixedPriceItemRequest>';
    }

    /**
     * Build XML for variation listing revision
     */
    private function buildVariationListingXml(int $ebayItemId, array $variationData): string
    {
        // Main listing images
        $mainImagesXml = '';
        foreach ($variationData['main_images'] as $url) {
            $mainImagesXml .= "<PictureURL>$url</PictureURL>\n";
        }

        // Variation-specific images
        $variationPicturesXml = '';
        if (!empty($variationData['variation_sets'])) {
            $variationPicturesXml .= '<Pictures>
                <VariationSpecificName>' . $variationData['variation_name'] . '</VariationSpecificName>';
            
            foreach ($variationData['variation_sets'] as $variationSet) {
                $variationPicturesXml .= '
                    <VariationSpecificPictureSet>
                        <VariationSpecificValue>' . htmlspecialchars($variationSet['value']) . '</VariationSpecificValue>';
                
                foreach ($variationSet['urls'] as $url) {
                    $variationPicturesXml .= '<PictureURL>' . htmlspecialchars($url) . '</PictureURL>';
                }
                
                $variationPicturesXml .= '</VariationSpecificPictureSet>';
            }
            
            $variationPicturesXml .= '</Pictures>';
        }

        return '<?xml version="1.0" encoding="utf-8"?>
            <ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
                <RequesterCredentials>
                    <eBayAuthToken>' . config('ebay.auth_token') . '</eBayAuthToken>
                </RequesterCredentials>
                <Item>
                    <ItemID>' . $ebayItemId . '</ItemID>
                    <PictureDetails>
                        ' . $mainImagesXml . '
                    </PictureDetails>
                    <Variations>
                        ' . $variationPicturesXml . '
                    </Variations>
                </Item>
            </ReviseFixedPriceItemRequest>';
    }

    /**
     * Call eBay ReviseFixedPriceItem API with resilience
     */
    private function callReviseItemApi(string $xmlPayload): array
    {
        try {
            $response = Http::withHeaders([
                'X-EBAY-API-COMPATIBILITY-LEVEL' => '967',
                'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
                'X-EBAY-API-APP-NAME' => $this->apiKey,
                'X-EBAY-API-CERT-NAME' => $this->apiSecret,
                'X-EBAY-API-CALL-NAME' => 'ReviseFixedPriceItem',
                'X-EBAY-API-SITEID' => '0',
                'Content-Type' => 'text/xml'
            ])
            ->timeout(60)
            ->connectTimeout(10)
            ->withBody($xmlPayload, 'text/xml')
            ->post($this->apiEndpoint);

            // Record successful API call for rate limiting
            $this->rateLimiter->recordEbayRequest();

            if ($response->successful()) {
                return $this->parseRevisionResponse($response->body());
            }

            return [
                'success' => false,
                'error' => 'HTTP ' . $response->status() . ': ' . $response->body()
            ];

        } catch (Exception $e) {
            Log::error('eBay API call failed', [
                'error' => $e->getMessage(),
                'payload_size' => strlen($xmlPayload)
            ]);
            
            throw $e;
        }
    }

    /**
     * Parse revision API response
     */
    private function parseRevisionResponse(string $xmlResponse): array
    {
        $xml = simplexml_load_string($xmlResponse);
        
        if (!$xml) {
            return ['success' => false, 'error' => 'Invalid XML response'];
        }

        $ack = (string) $xml->Ack;
        
        if ($ack === 'Success' || $ack === 'Warning') {
            return [
                'success' => true,
                'item_id' => (string) $xml->ItemID,
                'start_time' => (string) $xml->StartTime,
                'end_time' => (string) $xml->EndTime,
                'ack' => $ack,
                'fees' => $this->parseFees($xml),
                'response' => $xmlResponse
            ];
        }

        $errors = [];
        if (isset($xml->Errors)) {
            foreach ($xml->Errors as $error) {
                $errors[] = [
                    'code' => (string) $error->ErrorCode,
                    'message' => (string) $error->LongMessage,
                    'severity' => (string) $error->SeverityCode
                ];
            }
        }

        return [
            'success' => false,
            'errors' => $errors,
            'ack' => $ack,
            'response' => $xmlResponse
        ];
    }

    /**
     * Parse fees from revision response
     */
    private function parseFees($xml): array
    {
        $fees = [];
        if (isset($xml->Fees->Fee)) {
            foreach ($xml->Fees->Fee as $fee) {
                $fees[] = [
                    'name' => (string) $fee->Name,
                    'amount' => (float) $fee->Fee,
                    'currency' => (string) $fee->Fee['currencyID']
                ];
            }
        }
        return $fees;
    }
} 