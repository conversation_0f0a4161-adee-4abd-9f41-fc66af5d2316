<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class PerformanceMetricsService
{
    private const METRICS_TTL = 86400; // 24 hours
    private const AGGREGATION_WINDOW = 300; // 5 minutes

    /**
     * Record EPS upload performance metrics
     */
    public function recordEpsUploadMetrics(
        string $shopifyImageId,
        int $shopifyProductId,
        bool $success,
        int $durationMs,
        ?string $error = null,
        bool $wasCached = false
    ): void {
        $timestamp = now();
        $windowKey = $this->getWindowKey('eps_upload', $timestamp);
        
        $metrics = Cache::get($windowKey, $this->getEmptyMetrics());
        
        $metrics['total_uploads']++;
        $metrics['total_duration_ms'] += $durationMs;
        
        if ($success) {
            $metrics['successful_uploads']++;
            if ($wasCached) {
                $metrics['cache_hits']++;
            } else {
                $metrics['cache_misses']++;
                $metrics['actual_uploads']++;
            }
        } else {
            $metrics['failed_uploads']++;
            if ($error) {
                $metrics['errors'][$error] = ($metrics['errors'][$error] ?? 0) + 1;
            }
        }
        
        // Track performance by product
        $metrics['products'][$shopifyProductId] = ($metrics['products'][$shopifyProductId] ?? 0) + 1;
        
        Cache::put($windowKey, $metrics, self::METRICS_TTL);
        
        // Log high-level metrics
        $this->logPerformanceAlert($metrics, $durationMs, $success);
    }

    /**
     * Record listing revision performance metrics
     */
    public function recordListingRevisionMetrics(
        int $ebayItemId,
        bool $success,
        int $durationMs,
        int $imageCount,
        bool $hasVariations = false,
        ?string $error = null
    ): void {
        $timestamp = now();
        $windowKey = $this->getWindowKey('listing_revision', $timestamp);
        
        $metrics = Cache::get($windowKey, $this->getEmptyListingMetrics());
        
        $metrics['total_revisions']++;
        $metrics['total_duration_ms'] += $durationMs;
        $metrics['total_images_processed'] += $imageCount;
        
        if ($success) {
            $metrics['successful_revisions']++;
        } else {
            $metrics['failed_revisions']++;
            if ($error) {
                $metrics['errors'][$error] = ($metrics['errors'][$error] ?? 0) + 1;
            }
        }
        
        if ($hasVariations) {
            $metrics['variation_listings']++;
        } else {
            $metrics['simple_listings']++;
        }
        
        Cache::put($windowKey, $metrics, self::METRICS_TTL);
    }

    /**
     * Record queue performance metrics
     */
    public function recordQueueMetrics(): void
    {
        $timestamp = now();
        $windowKey = $this->getWindowKey('queue_performance', $timestamp);
        
        $queueStats = $this->getQueueStats();
        
        Cache::put($windowKey, $queueStats, self::METRICS_TTL);
    }

    /**
     * Get comprehensive performance dashboard data
     */
    public function getPerformanceDashboard(int $hours = 24): array
    {
        $endTime = now();
        $startTime = $endTime->copy()->subHours($hours);
        
        return [
            'eps_upload_metrics' => $this->aggregateMetrics('eps_upload', $startTime, $endTime),
            'listing_revision_metrics' => $this->aggregateMetrics('listing_revision', $startTime, $endTime),
            'queue_metrics' => $this->aggregateMetrics('queue_performance', $startTime, $endTime),
            'database_metrics' => $this->getDatabaseMetrics(),
            'cache_metrics' => $this->getCacheMetrics(),
            'system_health' => $this->getSystemHealthSummary(),
            'optimization_recommendations' => $this->getOptimizationRecommendations()
        ];
    }

    /**
     * Get empty metrics structure
     */
    private function getEmptyMetrics(): array
    {
        return [
            'total_uploads' => 0,
            'successful_uploads' => 0,
            'failed_uploads' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
            'actual_uploads' => 0,
            'total_duration_ms' => 0,
            'errors' => [],
            'products' => []
        ];
    }

    /**
     * Get empty listing metrics structure
     */
    private function getEmptyListingMetrics(): array
    {
        return [
            'total_revisions' => 0,
            'successful_revisions' => 0,
            'failed_revisions' => 0,
            'variation_listings' => 0,
            'simple_listings' => 0,
            'total_duration_ms' => 0,
            'total_images_processed' => 0,
            'errors' => []
        ];
    }

    /**
     * Get current queue statistics
     */
    private function getQueueStats(): array
    {
        return [
            'timestamp' => now()->timestamp,
            'eps_upload_high_pending' => $this->getQueueSize('eps-upload-high'),
            'eps_upload_medium_pending' => $this->getQueueSize('eps-upload-medium'),
            'listing_revision_pending' => $this->getQueueSize('listing-revision'),
            'eps_failed_pending' => $this->getQueueSize('eps-failed'),
            'total_workers' => $this->getActiveWorkerCount(),
            'memory_usage_mb' => memory_get_usage(true) / 1024 / 1024,
            'peak_memory_mb' => memory_get_peak_usage(true) / 1024 / 1024
        ];
    }

    /**
     * Get queue size for specific queue
     */
    private function getQueueSize(string $queueName): int
    {
        try {
            return DB::table('jobs')->where('queue', $queueName)->count();
        } catch (\Exception $e) {
            Log::warning('Failed to get queue size', ['queue' => $queueName, 'error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get active worker count (approximate)
     */
    private function getActiveWorkerCount(): int
    {
        try {
            // This is a rough estimate based on recent job processing
            return Cache::get('active_workers_count', 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Generate metrics window key
     */
    private function getWindowKey(string $type, $timestamp): string
    {
        $window = intval($timestamp->timestamp / self::AGGREGATION_WINDOW) * self::AGGREGATION_WINDOW;
        return "metrics:{$type}:" . date('Y-m-d:H:i', $window);
    }

    /**
     * Aggregate metrics over time period
     */
    private function aggregateMetrics(string $type, $startTime, $endTime): array
    {
        $aggregated = [];
        
        for ($time = $startTime; $time <= $endTime; $time->addMinutes(5)) {
            $windowKey = $this->getWindowKey($type, $time);
            $metrics = Cache::get($windowKey, []);
            
            if (!empty($metrics)) {
                foreach ($metrics as $key => $value) {
                    if (is_array($value)) {
                        foreach ($value as $subKey => $subValue) {
                            $aggregated[$key][$subKey] = ($aggregated[$key][$subKey] ?? 0) + $subValue;
                        }
                    } else {
                        $aggregated[$key] = ($aggregated[$key] ?? 0) + $value;
                    }
                }
            }
        }
        
        return $aggregated;
    }

    /**
     * Get database performance metrics
     */
    private function getDatabaseMetrics(): array
    {
        try {
            $slowQueries = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
            $connections = DB::select("SHOW GLOBAL STATUS LIKE 'Connections'");
            $threadConnected = DB::select("SHOW GLOBAL STATUS LIKE 'Threads_connected'");
            
            return [
                'slow_queries' => $slowQueries[0]->Value ?? 0,
                'total_connections' => $connections[0]->Value ?? 0,
                'active_connections' => $threadConnected[0]->Value ?? 0,
                'cache_table_size' => $this->getTableSize('eps_image_cache'),
                'jobs_table_size' => $this->getTableSize('jobs'),
                'failed_jobs_count' => DB::table('failed_jobs')->count()
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get database metrics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get table size information
     */
    private function getTableSize(string $tableName): int
    {
        try {
            $result = DB::select("SELECT COUNT(*) as count FROM {$tableName}");
            return $result[0]->count ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cache performance metrics
     */
    private function getCacheMetrics(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();
            
            return [
                'memory_used_mb' => ($info['used_memory'] ?? 0) / 1024 / 1024,
                'memory_peak_mb' => ($info['used_memory_peak'] ?? 0) / 1024 / 1024,
                'connected_clients' => $info['connected_clients'] ?? 0,
                'total_commands_processed' => $info['total_commands_processed'] ?? 0,
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info)
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get cache metrics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Calculate cache hit rate
     */
    private function calculateHitRate(array $info): float
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;
        
        return $total > 0 ? ($hits / $total) * 100 : 0;
    }

    /**
     * Get system health summary
     */
    private function getSystemHealthSummary(): array
    {
        $epsMetrics = $this->aggregateMetrics('eps_upload', now()->subHour(), now());
        $revisionMetrics = $this->aggregateMetrics('listing_revision', now()->subHour(), now());
        
        return [
            'eps_success_rate' => $this->calculateSuccessRate($epsMetrics, 'uploads'),
            'revision_success_rate' => $this->calculateSuccessRate($revisionMetrics, 'revisions'),
            'cache_hit_rate' => $this->calculateCacheHitRate($epsMetrics),
            'average_eps_duration' => $this->calculateAverageDuration($epsMetrics, 'uploads'),
            'average_revision_duration' => $this->calculateAverageDuration($revisionMetrics, 'revisions'),
            'queue_health' => $this->assessQueueHealth()
        ];
    }

    /**
     * Calculate success rate
     */
    private function calculateSuccessRate(array $metrics, string $type): float
    {
        $total = $metrics["total_{$type}"] ?? 0;
        $successful = $metrics["successful_{$type}"] ?? 0;
        
        return $total > 0 ? ($successful / $total) * 100 : 0;
    }

    /**
     * Calculate cache hit rate
     */
    private function calculateCacheHitRate(array $metrics): float
    {
        $hits = $metrics['cache_hits'] ?? 0;
        $total = ($metrics['cache_hits'] ?? 0) + ($metrics['cache_misses'] ?? 0);
        
        return $total > 0 ? ($hits / $total) * 100 : 0;
    }

    /**
     * Calculate average duration
     */
    private function calculateAverageDuration(array $metrics, string $type): float
    {
        $total = $metrics["total_{$type}"] ?? 0;
        $duration = $metrics['total_duration_ms'] ?? 0;
        
        return $total > 0 ? $duration / $total : 0;
    }

    /**
     * Assess queue health
     */
    private function assessQueueHealth(): string
    {
        $stats = $this->getQueueStats();
        
        $totalPending = $stats['eps_upload_high_pending'] + 
                       $stats['eps_upload_medium_pending'] + 
                       $stats['listing_revision_pending'];
        
        if ($totalPending > 1000) {
            return 'critical';
        } elseif ($totalPending > 500) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }

    /**
     * Get optimization recommendations
     */
    private function getOptimizationRecommendations(): array
    {
        $recommendations = [];
        
        // Check cache hit rate
        $cacheMetrics = $this->getCacheMetrics();
        if (($cacheMetrics['hit_rate'] ?? 100) < 80) {
            $recommendations[] = [
                'type' => 'cache',
                'priority' => 'high',
                'message' => 'Cache hit rate is below 80%. Consider increasing cache TTL or optimizing cache keys.'
            ];
        }
        
        // Check queue backlog
        $queueHealth = $this->assessQueueHealth();
        if ($queueHealth !== 'healthy') {
            $recommendations[] = [
                'type' => 'queue',
                'priority' => $queueHealth === 'critical' ? 'high' : 'medium',
                'message' => 'Queue backlog detected. Consider scaling workers or optimizing job processing.'
            ];
        }
        
        // Check EPS upload performance
        $epsMetrics = $this->aggregateMetrics('eps_upload', now()->subHour(), now());
        $epsSuccessRate = $this->calculateSuccessRate($epsMetrics, 'uploads');
        if ($epsSuccessRate < 95) {
            $recommendations[] = [
                'type' => 'eps',
                'priority' => 'medium',
                'message' => 'EPS upload success rate is below 95%. Check rate limiting and API health.'
            ];
        }
        
        return $recommendations;
    }

    /**
     * Log performance alerts
     */
    private function logPerformanceAlert(array $metrics, int $durationMs, bool $success): void
    {
        // Alert on slow uploads
        if ($durationMs > 10000) { // 10 seconds
            Log::warning('Slow EPS upload detected', [
                'duration_ms' => $durationMs,
                'success' => $success
            ]);
        }
        
        // Alert on high failure rate
        $total = $metrics['total_uploads'];
        $failed = $metrics['failed_uploads'];
        if ($total > 10 && ($failed / $total) > 0.1) { // 10% failure rate
            Log::warning('High EPS upload failure rate', [
                'total_uploads' => $total,
                'failed_uploads' => $failed,
                'failure_rate' => ($failed / $total) * 100
            ]);
        }
    }
} 