<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string|string>>
     */
    protected $listen = [
        \App\Events\EbayProductUpdated::class => [
            \App\Listeners\QueueEpsUploadJob::class,
        ],
        
        \App\Events\EpsImageUploaded::class => [
            \App\Listeners\ScheduleIncrementalRevision::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
} 