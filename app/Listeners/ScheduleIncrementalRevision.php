<?php

namespace App\Listeners;

use App\Events\EpsImageUploaded;
use App\Jobs\IncrementalListingRevision;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ScheduleIncrementalRevision implements ShouldQueue
{
    private const BATCH_DELAY_MINUTES = 10;
    private const CACHE_KEY_PREFIX = 'pending_revision_';

    public function handle(EpsImageUploaded $event): void
    {
        Log::info('EPS image uploaded, scheduling revision', [
            'ebay_item_id' => $event->ebayItemId,
            'shopify_image_id' => $event->shopifyImageId
        ]);

        $cacheKey = self::CACHE_KEY_PREFIX . $event->ebayItemId;
        
        // Add to batch of pending revisions
        $pendingImages = Cache::get($cacheKey, []);
        $pendingImages[$event->shopifyImageId] = [
            'eps_url' => $event->epsUrl,
            'variation_specific_value' => $event->variationSpecificValue,
            'uploaded_at' => now()->toISOString()
        ];
        
        // Cache for batch processing (extend TTL each time)
        Cache::put($cacheKey, $pendingImages, now()->addMinutes(self::BATCH_DELAY_MINUTES + 5));

        // Schedule delayed job for batch processing
        IncrementalListingRevision::dispatch($event->ebayItemId, $pendingImages)
            ->delay(now()->addMinutes(self::BATCH_DELAY_MINUTES))
            ->onQueue('listing-revision');

        Log::info('Incremental revision scheduled', [
            'ebay_item_id' => $event->ebayItemId,
            'delay_minutes' => self::BATCH_DELAY_MINUTES,
            'pending_images_count' => count($pendingImages)
        ]);
    }
} 