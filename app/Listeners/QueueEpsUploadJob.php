<?php

namespace App\Listeners;

use App\Events\EbayProductUpdated;
use App\Jobs\UploadImageToEps;
use App\Services\ImageResolutionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class QueueEpsUploadJob implements ShouldQueue
{
    public function __construct(
        private ImageResolutionService $imageResolutionService
    ) {}

    public function handle(EbayProductUpdated $event): void
    {
        Log::info('Processing EbayProductUpdated event', [
            'shopify_product_id' => $event->shopifyProductId,
            'ebay_item_id' => $event->ebayItemId,
            'action_type' => $event->actionType
        ]);

        // Process main product images
        $this->processProductImages($event);

        // Process variant-specific images
        $this->processVariantImages($event);
    }

    private function processProductImages(EbayProductUpdated $event): void
    {
        foreach ($event->productImages as $image) {
            $this->queueImageUpload($image, $event, null, null);
        }
    }

    private function processVariantImages(EbayProductUpdated $event): void
    {
        foreach ($event->variantImages as $variantImage) {
            $this->queueImageUpload(
                $variantImage['image'],
                $event,
                $variantImage['variant_id'],
                $variantImage['variation_specific_value']
            );
        }
    }

    private function queueImageUpload(
        array $image,
        EbayProductUpdated $event,
        ?int $variantId,
        ?string $variationValue
    ): void {
        $shopifyImageId = (string) $image['id'];
        
        // Check if already cached to avoid unnecessary jobs
        $cached = $this->imageResolutionService->getCachedEpsUrl($shopifyImageId);
        
        if (!$cached) {
            UploadImageToEps::dispatch(
                $image['src'],
                $shopifyImageId,
                $event->shopifyProductId,
                $event->ebayItemId,
                $variantId,
                $variationValue
            );

            Log::info('Queued EPS upload job', [
                'shopify_image_id' => $shopifyImageId,
                'shopify_product_id' => $event->shopifyProductId
            ]);
        }
    }
} 