<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessFailedEpsUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $imageUrl;
    public string $shopifyImageId;
    public int $shopifyProductId;
    public int $ebayItemId;
    public string $errorMessage;

    public function __construct(
        string $imageUrl,
        string $shopifyImageId,
        int $shopifyProductId,
        int $ebayItemId,
        string $errorMessage
    ) {
        $this->imageUrl = $imageUrl;
        $this->shopifyImageId = $shopifyImageId;
        $this->shopifyProductId = $shopifyProductId;
        $this->ebayItemId = $ebayItemId;
        $this->errorMessage = $errorMessage;
        $this->onQueue('eps-failed');
    }

    public function handle(): void
    {
        // Log for manual review and potential retry
        Log::error('EPS upload permanently failed - manual review required', [
            'shopify_image_id' => $this->shopifyImageId,
            'shopify_product_id' => $this->shopifyProductId,
            'ebay_item_id' => $this->ebayItemId,
            'image_url' => $this->imageUrl,
            'error' => $this->errorMessage
        ]);

        // TODO: Could implement notification system here
        // TODO: Could store in dedicated failed_uploads table for dashboard
    }
} 