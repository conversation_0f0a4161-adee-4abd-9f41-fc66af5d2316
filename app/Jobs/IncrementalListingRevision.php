<?php

namespace App\Jobs;

use App\Services\EbayListingRevisionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Cache;

class IncrementalListingRevision implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 2;
    public int $timeout = 60;

    public int $ebayItemId;
    public array $newEpsUrls;

    public function __construct(int $ebayItemId, array $newEpsUrls)
    {
        $this->ebayItemId = $ebayItemId;
        $this->newEpsUrls = $newEpsUrls;
        $this->onQueue('listing-revision');
    }

    public function handle(EbayListingRevisionService $revisionService): void
    {
        try {
            // Clear cache to prevent duplicate processing
            $cacheKey = 'pending_revision_' . $this->ebayItemId;
            $cachedImages = Cache::get($cacheKey, []);
            
            // Merge with new EPS URLs
            $allNewUrls = array_merge($this->newEpsUrls, $cachedImages);
            
            Log::info('Starting incremental listing revision', [
                'ebay_item_id' => $this->ebayItemId,
                'new_eps_urls_count' => count($this->newEpsUrls),
                'cached_urls_count' => count($cachedImages),
                'total_urls_count' => count($allNewUrls)
            ]);

            $result = $revisionService->reviseItemImages(
                $this->ebayItemId,
                $allNewUrls
            );

            if ($result['success']) {
                // Clear cache on successful revision
                Cache::forget($cacheKey);
                
                Log::info('Incremental listing revision successful', [
                    'ebay_item_id' => $this->ebayItemId,
                    'revision_details' => $result
                ]);
            } else {
                throw new Exception('Revision failed: ' . ($result['error'] ?? json_encode($result['errors'] ?? [])));
            }

        } catch (Exception $e) {
            Log::error('Incremental listing revision failed', [
                'ebay_item_id' => $this->ebayItemId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
} 