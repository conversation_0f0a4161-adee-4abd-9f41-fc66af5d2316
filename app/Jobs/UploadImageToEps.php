<?php

namespace App\Jobs;

use App\Services\EpsUploadService;
use App\Events\EpsImageUploaded;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class UploadImageToEps implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 120;
    public int $backoff = 30;

    public string $imageUrl;
    public string $shopifyImageId;
    public int $shopifyProductId;
    public ?int $shopifyVariantId;
    public ?string $variationSpecificValue;
    public int $ebayItemId;

    public function __construct(
        string $imageUrl,
        string $shopifyImageId,
        int $shopifyProductId,
        int $ebayItemId,
        ?int $shopifyVariantId = null,
        ?string $variationSpecificValue = null
    ) {
        $this->imageUrl = $imageUrl;
        $this->shopifyImageId = $shopifyImageId;
        $this->shopifyProductId = $shopifyProductId;
        $this->ebayItemId = $ebayItemId;
        $this->shopifyVariantId = $shopifyVariantId;
        $this->variationSpecificValue = $variationSpecificValue;

        // Set queue priority based on image type
        $this->onQueue($this->determineQueuePriority());
    }

    public function handle(EpsUploadService $epsUploadService): void
    {
        try {
            Log::info('Starting EPS upload', [
                'shopify_image_id' => $this->shopifyImageId,
                'shopify_product_id' => $this->shopifyProductId,
                'attempt' => $this->attempts()
            ]);

            $epsUrl = $epsUploadService->uploadImage(
                $this->imageUrl,
                $this->shopifyImageId,
                $this->shopifyProductId,
                $this->shopifyVariantId,
                $this->variationSpecificValue
            );

            if ($epsUrl) {
                // Dispatch success event for incremental revision
                EpsImageUploaded::dispatch(
                    $this->shopifyProductId,
                    $this->ebayItemId,
                    $this->shopifyImageId,
                    $epsUrl,
                    $this->variationSpecificValue
                );

                Log::info('EPS upload successful', [
                    'shopify_image_id' => $this->shopifyImageId,
                    'eps_url' => $epsUrl
                ]);
            } else {
                // Check if it's a rate limiting issue
                if ($this->isRateLimitingIssue()) {
                    Log::info('EPS upload delayed due to rate limiting', [
                        'shopify_image_id' => $this->shopifyImageId,
                        'attempt' => $this->attempts()
                    ]);
                    
                    // Release job back to queue with exponential backoff
                    $delay = min(300, 30 * pow(2, $this->attempts() - 1)); // Max 5 minute delay
                    $this->release($delay);
                    return;
                }
                
                throw new Exception('EPS upload returned null URL');
            }

        } catch (Exception $e) {
            Log::error('EPS upload job failed', [
                'shopify_image_id' => $this->shopifyImageId,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts()
            ]);

            // If max attempts reached, send to dead letter queue
            if ($this->attempts() >= $this->tries) {
                $this->sendToDeadLetterQueue($e);
            }

            throw $e;
        }
    }

    /**
     * Check if failure is due to rate limiting
     */
    private function isRateLimitingIssue(): bool
    {
        // This could check various indicators of rate limiting
        // For now, we'll assume it's a possibility and handle gracefully
        return $this->attempts() <= 2; // Only retry first 2 attempts for rate limiting
    }

    /**
     * Calculate backoff delay with jitter
     */
    public function backoff(): array
    {
        $baseDelay = 30; // 30 seconds base delay
        $maxDelay = 300; // 5 minutes max delay
        
        $delay = min($maxDelay, $baseDelay * pow(2, $this->attempts() - 1));
        
        // Add jitter (±25%)
        $jitter = $delay * 0.25;
        $actualDelay = $delay + random_int(-$jitter, $jitter);
        
        return [max(1, $actualDelay)];
    }

    public function failed(Exception $exception): void
    {
        Log::error('EPS upload job permanently failed', [
            'shopify_image_id' => $this->shopifyImageId,
            'shopify_product_id' => $this->shopifyProductId,
            'error' => $exception->getMessage()
        ]);
    }

    private function determineQueuePriority(): string
    {
        // Primary product images get high priority
        if (!$this->shopifyVariantId) {
            return 'eps-upload-high';
        }
        
        // Variation images get medium priority
        return 'eps-upload-medium';
    }

    private function sendToDeadLetterQueue(Exception $exception): void
    {
        // Queue for manual review/retry
        ProcessFailedEpsUpload::dispatch(
            $this->imageUrl,
            $this->shopifyImageId,
            $this->shopifyProductId,
            $this->ebayItemId,
            $exception->getMessage()
        )->onQueue('eps-failed');
    }
} 