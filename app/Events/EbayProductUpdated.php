<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EbayProductUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $shopifyProductId;
    public int $ebayItemId;
    public array $productImages;
    public array $variantImages;
    public string $actionType; // 'created' or 'updated'

    public function __construct(
        int $shopifyProductId,
        int $ebayItemId,
        array $productImages,
        array $variantImages = [],
        string $actionType = 'updated'
    ) {
        $this->shopifyProductId = $shopifyProductId;
        $this->ebayItemId = $ebayItemId;
        $this->productImages = $productImages;
        $this->variantImages = $variantImages;
        $this->actionType = $actionType;
    }
} 