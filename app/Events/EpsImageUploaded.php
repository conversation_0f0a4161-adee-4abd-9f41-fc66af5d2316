<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EpsImageUploaded
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $shopifyProductId;
    public int $ebayItemId;
    public string $shopifyImageId;
    public string $epsUrl;
    public ?string $variationSpecificValue;

    public function __construct(
        int $shopifyProductId,
        int $ebayItemId,
        string $shopifyImageId,
        string $epsUrl,
        ?string $variationSpecificValue = null
    ) {
        $this->shopifyProductId = $shopifyProductId;
        $this->ebayItemId = $ebayItemId;
        $this->shopifyImageId = $shopifyImageId;
        $this->epsUrl = $epsUrl;
        $this->variationSpecificValue = $variationSpecificValue;
    }
} 