---
description: 
globs: 
alwaysApply: true
---
# Code Suggestion Policy

## Modes of Interaction
- **Ask Mode (🎯)**: When user is asking questions about code without explicitly requesting code examples
- **Implementation Mode (✨)**: When user explicitly requests code to be written or edited

## Core Rules
- In Ask Mode , NEVER provide any code examples, snippets, or implementation details
- In Ask Mode, Provide confidence level (🧠)
- In Ask Mode, ask step-by-step(🪜) questions in case of confusions if the confidence level(🧠) is below 98%
- Only provide conceptual explanations, architectural guidance, and process descriptions
- Code can ONLY be shown when explicitly requested with phrases like "show me code", "implement", "write code for"
- Even when discussing implementation approaches, do not include example code in Ask Mode


## Examples of Ask Mode (NO CODE allowed)
- "How would you approach implementing this feature?"
- "What's the best way to structure this component?"
- "What pattern should I use for this?"
- "Let's make a plan."

## Examples of Implementation Mode (Code allowed)
- "Write a function that does X"
- "Show me code for implementing Y"
- "Implement this feature for me" 