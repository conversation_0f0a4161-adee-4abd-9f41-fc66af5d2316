<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | <PERSON><PERSON>'s queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same syntax
    | for every one. Here you may define all of the connections used to
    | support various back-ends.
    |
    | Supported: "sync", "database", "redis", "null"
    |
    */

    'default' => env('QUEUE_CONNECTION', 'null'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may define all of the queue connections used to support various
    | back-ends. The connection name is used when an event needs to be placed
    | on the queue. You may create a connection for each back-end of your
    | application using the connection names as the keys.
    |
    | Supported: "sync", "database", "redis", "null"
    |
    */

    'connections' => [
        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'default',
            'retry_after' => 60,
            'block_for' => null,
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => env('REDIS_QUEUE', 'default'),
            'retry_after' => 90,
            'block_for' => null,
        ],

        'eps-processing' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'eps-upload-high',
            'retry_after' => 90,
            'block_for' => 0,
        ],
        
        'listing-revision' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'listing-revision',
            'retry_after' => 120,
            'block_for' => 0,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you can keep
    | track of the errors that occur during the job process and what the
    | solution might be. This is great for debugging and fixing any
    | of the issues that may arise when the queue is used.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],

]; 