<?php

namespace App\Services\AI\Knowledge;

use App\Models\AI\AIKnowledgeDocument;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class KnowledgeIngestionService
{
    private DocumentProcessor $documentProcessor;

    public function __construct(DocumentProcessor $documentProcessor)
    {
        $this->documentProcessor = $documentProcessor;
    }

    /**
     * Ingest knowledge from file.
     */
    public function ingestFromFile(
        string $filePath,
        string $category,
        ?string $subcategory = null,
        array $tags = []
    ): ?AIKnowledgeDocument {
        try {
            if (!Storage::exists($filePath)) {
                throw new Exception("File not found: {$filePath}");
            }

            $content = Storage::get($filePath);
            $title = $this->extractTitleFromPath($filePath);

            return $this->documentProcessor->processDocument(
                $title,
                $content,
                $category,
                $subcategory,
                $tags,
                $filePath
            );
        } catch (Exception $e) {
            Log::error('Failed to ingest knowledge from file', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Ingest knowledge from directory.
     */
    public function ingestFromDirectory(
        string $directoryPath,
        string $category,
        ?string $subcategory = null,
        array $tags = []
    ): array {
        $results = [];

        try {
            $files = Storage::files($directoryPath);

            Log::info('Directory ingestion started', [
                'directory' => $directoryPath,
                'files_found' => count($files),
                'files' => $files,
            ]);

            foreach ($files as $file) {
                Log::info('Processing file', [
                    'file' => $file,
                    'is_text_file' => $this->isTextFile($file),
                ]);

                if ($this->isTextFile($file)) {
                    $document = $this->ingestFromFile($file, $category, $subcategory, $tags);
                    if ($document) {
                        $results[] = $document;
                        Log::info('Successfully processed file', [
                            'file' => $file,
                            'document_id' => $document->id,
                        ]);
                    } else {
                        Log::warning('Failed to process file', ['file' => $file]);
                    }
                }
            }

            Log::info('Completed directory ingestion', [
                'directory' => $directoryPath,
                'files_processed' => count($results),
            ]);
        } catch (Exception $e) {
            Log::error('Failed to ingest knowledge from directory', [
                'directory' => $directoryPath,
                'error' => $e->getMessage(),
            ]);
        }

        return $results;
    }

    /**
     * Ingest app domain knowledge.
     */
    public function ingestAppDomainKnowledge(): array
    {
        $documents = [];

        // First, try to ingest from files
        $fileDocuments = $this->ingestFromDirectory(
            'ai_knowledge/app_domain',
            'app_domain',
            null,
            ['app_domain', 'integration', 'guide']
        );
        $documents = array_merge($documents, $fileDocuments);

        // Then add programmatically generated content
        $documents = array_merge($documents, $this->ingestProfileKnowledge());
        $documents = array_merge($documents, $this->ingestSyncKnowledge());
        $documents = array_merge($documents, $this->ingestIntegrationKnowledge());

        return $documents;
    }

    /**
     * Ingest profile management knowledge.
     */
    private function ingestProfileKnowledge(): array
    {
        $documents = [];

        // Profile Creation Guide
        $profileCreationContent = $this->generateProfileCreationGuide();
        Log::info('Processing Profile Creation Guide', ['content_length' => strlen($profileCreationContent)]);

        $doc = $this->documentProcessor->processDocument(
            'eBay Profile Creation Guide',
            $profileCreationContent,
            'app_domain',
            'profile_management',
            ['profile', 'creation', 'guide', 'ebay']
        );
        if ($doc) {
            $documents[] = $doc;
            Log::info('Successfully processed Profile Creation Guide', ['doc_id' => $doc->id]);
        } else {
            Log::error('Failed to process Profile Creation Guide');
        }

        // Category Selection Guide
        $categoryGuideContent = $this->generateCategorySelectionGuide();
        $doc = $this->documentProcessor->processDocument(
            'eBay Category Selection Guide',
            $categoryGuideContent,
            'app_domain',
            'profile_management',
            ['category', 'selection', 'ebay', 'mapping']
        );
        if ($doc) $documents[] = $doc;

        // Profile Attributes Guide
        $attributesGuideContent = $this->generateProfileAttributesGuide();
        $doc = $this->documentProcessor->processDocument(
            'Profile Attributes Configuration Guide',
            $attributesGuideContent,
            'app_domain',
            'profile_management',
            ['attributes', 'configuration', 'profile', 'item_specifics']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest sync operations knowledge.
     */
    private function ingestSyncKnowledge(): array
    {
        $documents = [];

        // Inventory Sync Guide
        $inventorySyncContent = $this->generateInventorySyncGuide();
        $doc = $this->documentProcessor->processDocument(
            'Inventory Synchronization Guide',
            $inventorySyncContent,
            'app_domain',
            'sync_operations',
            ['inventory', 'sync', 'shopify', 'ebay']
        );
        if ($doc) $documents[] = $doc;

        // Product Upload Process
        $productUploadContent = $this->generateProductUploadGuide();
        $doc = $this->documentProcessor->processDocument(
            'Product Upload Process Guide',
            $productUploadContent,
            'app_domain',
            'sync_operations',
            ['product', 'upload', 'process', 'sync']
        );
        if ($doc) $documents[] = $doc;

        // Order Sync Guide
        $orderSyncContent = $this->generateOrderSyncGuide();
        $doc = $this->documentProcessor->processDocument(
            'Order Synchronization Guide',
            $orderSyncContent,
            'app_domain',
            'sync_operations',
            ['order', 'sync', 'fulfillment', 'tracking']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest integration workflows knowledge.
     */
    private function ingestIntegrationKnowledge(): array
    {
        $documents = [];

        // Initial Setup Guide
        $setupContent = $this->generateInitialSetupGuide();
        $doc = $this->documentProcessor->processDocument(
            'Initial Setup and Configuration Guide',
            $setupContent,
            'app_domain',
            'integration_workflows',
            ['setup', 'configuration', 'onboarding', 'authentication']
        );
        if ($doc) $documents[] = $doc;

        // Troubleshooting Guide
        $troubleshootingContent = $this->generateTroubleshootingGuide();
        $doc = $this->documentProcessor->processDocument(
            'Common Issues and Troubleshooting Guide',
            $troubleshootingContent,
            'troubleshooting',
            'sync_issues',
            ['troubleshooting', 'errors', 'issues', 'solutions']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest platform-specific knowledge.
     */
    public function ingestPlatformKnowledge(): array
    {
        $documents = [];

        // First, try to ingest from files
        $shopifyDocuments = $this->ingestFromDirectory(
            'ai_knowledge/shopify_platform',
            'shopify_platform',
            null,
            ['shopify', 'platform', 'guide']
        );
        $documents = array_merge($documents, $shopifyDocuments);

        $ebayDocuments = $this->ingestFromDirectory(
            'ai_knowledge/ebay_platform',
            'ebay_platform',
            null,
            ['ebay', 'platform', 'guide']
        );
        $documents = array_merge($documents, $ebayDocuments);

        // Also ingest troubleshooting files
        $troubleshootingDocuments = $this->ingestFromDirectory(
            'ai_knowledge/troubleshooting',
            'troubleshooting',
            null,
            ['troubleshooting', 'issues', 'guide']
        );
        $documents = array_merge($documents, $troubleshootingDocuments);

        // Then add programmatically generated content
        $shopifyContent = $this->generateShopifyPlatformGuide();
        $doc = $this->documentProcessor->processDocument(
            'Shopify Platform Integration Guide',
            $shopifyContent,
            'shopify_platform',
            'api_integration',
            ['shopify', 'platform', 'api', 'products', 'variants']
        );
        if ($doc) $documents[] = $doc;

        $ebayContent = $this->generateEbayPlatformGuide();
        $doc = $this->documentProcessor->processDocument(
            'eBay Platform Integration Guide',
            $ebayContent,
            'ebay_platform',
            'listing_requirements',
            ['ebay', 'platform', 'categories', 'policies', 'requirements']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Extract title from file path.
     */
    private function extractTitleFromPath(string $filePath): string
    {
        $pathInfo = pathinfo($filePath);
        $filename = $pathInfo['filename']; // Gets filename without extension
        return ucwords(str_replace(['_', '-'], ' ', $filename));
    }

    /**
     * Check if file is a text file.
     */
    private function isTextFile(string $filePath): bool
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, ['txt', 'md', 'markdown']);
    }

    /**
     * Generate profile creation guide content.
     */
    private function generateProfileCreationGuide(): string
    {
        return <<<EOT
# eBay Profile Creation Guide

## Overview
eBay profiles in our integration app are templates that define how your Shopify products will be listed on eBay. Each profile contains settings for category, item specifics, pricing rules, shipping policies, and more.

## Creating a New Profile

### Step 1: Profile Basic Information
- **Profile Name**: Choose a descriptive name that identifies the product type or category
- **Description**: Optional description to help you identify the profile's purpose

### Step 2: eBay Category Selection
- Select the most appropriate eBay category for your products
- Use the category search feature to find specific categories
- Consider browsing the category hierarchy for better matches
- Check category requirements and restrictions

### Step 3: Item Condition
- New: Brand new, unused items in original packaging
- New with defects: New items with minor defects or missing original packaging
- New without tags: New items without original tags or packaging
- Used: Previously owned items in good condition
- For parts or not working: Items that don't function properly

### Step 4: Item Specifics and Attributes
- Fill in required item specifics for your chosen category
- Use Shopify product data to auto-populate when possible
- Ensure accuracy as these affect search visibility
- Leave optional fields blank if information is not available

### Step 5: Pricing Configuration
- **Fixed Price**: Set a specific price for all products using this profile
- **Shopify Price**: Use the product's Shopify price
- **Price Adjustment**: Add percentage or fixed amount to Shopify price
- **Best Offer**: Enable to allow buyers to make offers

### Step 6: Shipping Settings
- Select from your existing eBay shipping policies
- Ensure shipping costs are competitive
- Consider offering free shipping with higher item prices

### Step 7: Return Policy
- Select from your existing eBay return policies
- Clear return policies increase buyer confidence
- Consider eBay's managed returns program

### Step 8: Payment Policy
- Select from your existing eBay payment policies
- PayPal and managed payments are recommended

## Best Practices
- Create separate profiles for different product categories
- Use descriptive profile names
- Regularly review and update profiles based on performance
- Test profiles with a few products before bulk uploads
- Monitor eBay policy changes that might affect your profiles

## Common Issues
- **Category Mismatch**: Ensure selected category matches your product type
- **Missing Required Attributes**: Check category requirements before creating profile
- **Pricing Errors**: Verify pricing rules don't result in unrealistic prices
- **Policy Conflicts**: Ensure shipping, return, and payment policies are compatible
EOT;
    }

    /**
     * Generate category selection guide content.
     */
    private function generateCategorySelectionGuide(): string
    {
        return <<<EOT
# eBay Category Selection Guide

## Understanding eBay Categories
eBay uses a hierarchical category system to organize listings. Selecting the right category is crucial for search visibility, buyer discovery, fee calculation, required item specifics, and listing policies.

## Category Selection Process
1. **Start with Product Type**: Identify the primary function or type of your product
2. **Use Category Search**: Enter keywords related to your product and review suggested categories
3. **Navigate Category Hierarchy**: Start with broad categories and drill down to more specific subcategories

## Popular Categories for Common Products

### Electronics
- Cell Phones & Smartphones (9355)
- Cell Phone Accessories (9394)
- Computers/Tablets & Networking (58058)
- Consumer Electronics (293)

### Fashion
- Clothing, Shoes & Accessories (11450)
- Women's Clothing (15724)
- Men's Clothing (1059)
- Jewelry & Watches (281)

### Home & Garden
- Home & Garden (11700)
- Home Décor (20081)
- Kitchen, Dining & Bar (20625)

## Best Practices
- Create separate profiles for different product categories
- Research competitor placements in categories
- Consider buyer behavior in each category
- Stay updated with eBay category changes
- Test different categories for performance

## Common Issues
- **Category Mismatch**: Ensure selected category matches your product type
- **Restricted Categories**: Some categories require pre-approval from eBay
- **High-Fee Categories**: Be aware of categories with higher final value fees
- **Special Requirements**: Some categories need specific information (VIN numbers, model numbers, etc.)
EOT;
    }

    /**
     * Generate profile attributes guide content.
     */
    private function generateProfileAttributesGuide(): string
    {
        return <<<EOT
# Profile Attributes Configuration Guide

## Understanding Item Specifics
Item specifics are structured data fields that provide detailed information about your products. They help buyers find your items and improve search visibility.

## Required vs Optional Attributes
- **Required**: Must be filled for successful listing
- **Recommended**: Improve search visibility and buyer confidence
- **Optional**: Additional details that may help with sales

## Common Attribute Types

### Product Identifiers
- **Brand**: Manufacturer or brand name
- **Model**: Specific model number or name
- **MPN (Manufacturer Part Number)**: Unique identifier from manufacturer
- **UPC/EAN**: Universal product codes for retail items

### Physical Characteristics
- **Color**: Primary color or color combination
- **Size**: Dimensions, clothing sizes, or capacity
- **Material**: What the item is made from
- **Weight**: Product weight (important for shipping)

### Condition Details
- **Condition**: New, Used, Refurbished, etc.
- **Condition Description**: Additional details about item condition
- **Defects**: Any known issues or missing parts

### Technical Specifications
- **Compatibility**: What devices/systems the item works with
- **Features**: Key product features and capabilities
- **Specifications**: Technical details like dimensions, power requirements

## Best Practices for Attributes

### Accuracy is Critical
- Use exact information from manufacturer specifications
- Don't guess or estimate technical details
- Verify compatibility information before listing
- Update attributes if product specifications change

### Use Shopify Data
- Map Shopify product fields to eBay attributes
- Use product variants for size/color variations
- Leverage Shopify metafields for additional details
- Maintain consistency between platforms

### Optimize for Search
- Include relevant keywords in appropriate fields
- Use standard terminology that buyers search for
- Fill in as many relevant attributes as possible
- Consider seasonal or trending terms

## Category-Specific Attributes

### Electronics
- Brand, Model, MPN, UPC
- Connectivity (WiFi, Bluetooth, USB)
- Screen Size, Resolution, Storage Capacity
- Operating System, Processor Type
- Warranty Information

### Clothing & Accessories
- Brand, Size Type, Size, Color
- Material, Care Instructions
- Style, Fit, Occasion
- Season, Pattern, Theme

### Home & Garden
- Brand, Model, Material, Color
- Dimensions, Weight, Capacity
- Power Source, Assembly Required
- Indoor/Outdoor Use, Style

### Automotive
- Brand, Manufacturer Part Number
- Fitment (Year, Make, Model)
- Placement on Vehicle
- Warranty, Condition

## Common Mistakes

### Incomplete Information
- Leaving required fields empty
- Using generic or vague descriptions
- Not researching proper attribute values
- Ignoring category-specific requirements

### Incorrect Mapping
- Mismatching Shopify fields to eBay attributes
- Using wrong units of measurement
- Incorrect compatibility information
- Outdated or obsolete specifications

### Keyword Stuffing
- Adding irrelevant keywords to attributes
- Using attributes for promotional text
- Repeating information across multiple fields
- Violating eBay's keyword policies

## Automation Tips

### Shopify Integration
- Use product tags for attribute mapping
- Leverage metafields for technical specifications
- Set up variant mapping for size/color attributes
- Create templates for common product types

### Bulk Updates
- Use CSV imports for large catalogs
- Create attribute templates by category
- Set up rules for automatic attribute population
- Regular audits to ensure accuracy

## Troubleshooting Attribute Issues

### Missing Required Attributes
- Check category requirements in eBay Seller Hub
- Review error messages for specific missing fields
- Use eBay's category-specific guides
- Contact support for category-specific questions

### Attribute Validation Errors
- Verify attribute values match eBay's accepted values
- Check for typos or formatting issues
- Ensure numeric values are in correct units
- Review character limits for text fields

### Search Visibility Issues
- Audit attribute completeness
- Compare with successful competitor listings
- Test different attribute combinations
- Monitor search performance metrics
EOT;
    }

    /**
     * Generate inventory sync guide content.
     */
    private function generateInventorySyncGuide(): string
    {
        return <<<EOT
# Inventory Synchronization Guide

## Overview
Inventory synchronization ensures that your stock levels are consistent between Shopify and eBay. This prevents overselling and maintains accurate availability information for customers.

## How Inventory Sync Works
1. **Shopify to eBay**: When inventory changes in Shopify, it updates on eBay
2. **eBay to Shopify**: When items sell on eBay, inventory decreases in Shopify
3. **Real-time Updates**: Changes are processed automatically within minutes
4. **Conflict Resolution**: System handles simultaneous changes intelligently

## Sync Settings Configuration

### Enable Inventory Sync
- Navigate to Settings > Sync Configuration
- Toggle "Enable Inventory Sync" to ON
- Choose sync direction (bidirectional recommended)
- Set sync frequency (real-time, hourly, daily)

### Sync Thresholds
- **Minimum Stock Level**: Don't sync below this quantity
- **Maximum Stock Level**: Cap the quantity synced to eBay
- **Buffer Stock**: Reserve quantity for Shopify-only sales
- **Out of Stock Behavior**: End listing or set to zero

### Variant Mapping
- Map Shopify variants to eBay variations
- Handle size, color, and style combinations
- Set up SKU-based matching
- Configure default mappings for new products

## Best Practices

### Stock Management
- Maintain buffer stock for popular items
- Set realistic minimum stock levels
- Monitor sync performance regularly
- Use inventory alerts for low stock

### Conflict Prevention
- Avoid manual inventory changes during sync
- Use consistent SKU formats across platforms
- Set up proper variant mappings
- Monitor for sync errors regularly

### Performance Optimization
- Sync only active products
- Use batch updates for large inventories
- Schedule heavy syncs during off-peak hours
- Monitor API rate limits

## Common Issues and Solutions

### Sync Delays
- **Cause**: High API traffic or rate limiting
- **Solution**: Increase sync intervals, optimize batch sizes
- **Prevention**: Monitor API usage, use off-peak scheduling

### Inventory Mismatches
- **Cause**: Failed sync operations or manual changes
- **Solution**: Run manual sync, check error logs
- **Prevention**: Avoid manual changes, use proper workflows

### Overselling
- **Cause**: Sync delays or insufficient buffer stock
- **Solution**: Increase buffer stock, faster sync frequency
- **Prevention**: Set conservative stock levels, monitor closely

### Variant Mapping Issues
- **Cause**: Inconsistent SKUs or missing mappings
- **Solution**: Review and update variant mappings
- **Prevention**: Use standardized SKU formats

## Monitoring and Troubleshooting

### Sync Status Dashboard
- View real-time sync status
- Monitor success/failure rates
- Check last sync timestamps
- Review error logs and messages

### Error Types
- **API Errors**: Rate limits, authentication issues
- **Data Errors**: Invalid quantities, missing SKUs
- **Mapping Errors**: Unmatched variants, duplicate SKUs
- **System Errors**: Server issues, network problems

### Resolution Steps
1. Check error logs for specific issues
2. Verify API credentials and permissions
3. Review product and variant mappings
4. Test sync with individual products
5. Contact support for persistent issues

## Advanced Configuration

### Custom Sync Rules
- Set category-specific sync rules
- Configure seasonal inventory adjustments
- Use tags for selective syncing
- Set up automated stock replenishment

### Integration with Other Tools
- Connect with inventory management systems
- Use webhooks for real-time updates
- Integrate with fulfillment services
- Set up automated reordering

### Reporting and Analytics
- Track sync performance metrics
- Monitor inventory turnover rates
- Analyze overselling incidents
- Generate stock level reports
EOT;
    }

    /**
     * Generate product upload guide content.
     */
    private function generateProductUploadGuide(): string
    {
        return <<<EOT
# Product Upload Process Guide

## Overview
The product upload process transfers your Shopify products to eBay using configured profiles. Understanding this process helps ensure successful listings and optimal performance.

## Upload Process Flow
1. **Product Selection**: Choose products to upload
2. **Profile Assignment**: Assign appropriate eBay profile
3. **Data Validation**: Check for required fields and compliance
4. **Image Processing**: Optimize and upload product images
5. **Listing Creation**: Create eBay listing with all details
6. **Status Monitoring**: Track upload progress and results

## Preparing Products for Upload

### Product Information
- **Title**: Clear, descriptive, keyword-rich
- **Description**: Detailed, formatted, compliant with eBay policies
- **Images**: High-quality, multiple angles, proper dimensions
- **Price**: Competitive, includes all costs
- **Inventory**: Accurate stock levels

### Shopify Requirements
- Products must be published and active
- All variants should have SKUs
- Images should be high resolution
- Product types should be properly categorized
- Inventory tracking should be enabled

### eBay Compliance
- Follow eBay listing policies
- Ensure category appropriateness
- Include required item specifics
- Comply with image requirements
- Meet title and description guidelines

## Upload Methods

### Individual Upload
- Select single product from product list
- Choose or create appropriate profile
- Review and modify listing details
- Upload immediately or schedule

### Bulk Upload
- Select multiple products using filters
- Assign profiles by category or tags
- Queue uploads for batch processing
- Monitor progress in upload dashboard

### Collection-Based Upload
- Upload entire Shopify collections
- Automatic profile assignment by rules
- Maintain collection organization on eBay
- Sync collection changes automatically

### Scheduled Upload
- Set up automated upload schedules
- Upload new products automatically
- Update existing listings regularly
- Process during optimal times

## Upload Status and Monitoring

### Status Types
- **Pending**: Queued for upload
- **Processing**: Currently being uploaded
- **Completed**: Successfully listed on eBay
- **Failed**: Upload failed with errors
- **Partial**: Some variants uploaded successfully

### Progress Tracking
- Real-time upload progress indicators
- Detailed status for each product/variant
- Error messages and resolution suggestions
- Estimated completion times

### Error Handling
- Automatic retry for temporary failures
- Detailed error logs and explanations
- Suggested fixes for common issues
- Manual retry options for failed uploads

## Common Upload Issues

### Image Problems
- **Issue**: Images too large or wrong format
- **Solution**: Resize images, use supported formats (JPG, PNG)
- **Prevention**: Optimize images before upload

### Missing Information
- **Issue**: Required fields empty or invalid
- **Solution**: Complete product information, verify data
- **Prevention**: Use data validation before upload

### Category Restrictions
- **Issue**: Product doesn't meet category requirements
- **Solution**: Choose appropriate category, add required specifics
- **Prevention**: Research category requirements beforehand

### Policy Violations
- **Issue**: Content violates eBay policies
- **Solution**: Modify title/description to comply
- **Prevention**: Review eBay policies regularly

## Optimization Tips

### Performance
- Upload during off-peak hours
- Use batch processing for large catalogs
- Optimize images before upload
- Monitor API rate limits

### Success Rates
- Complete all required fields
- Use high-quality images
- Write compelling titles and descriptions
- Choose appropriate categories

### Efficiency
- Create reusable profiles
- Use automation rules
- Set up proper variant mappings
- Monitor and optimize regularly

## Post-Upload Management

### Listing Verification
- Check listings on eBay for accuracy
- Verify images and descriptions
- Confirm pricing and shipping details
- Test buyer experience

### Performance Monitoring
- Track listing views and watchers
- Monitor conversion rates
- Analyze search ranking
- Adjust strategies based on data

### Ongoing Maintenance
- Update listings regularly
- Refresh images and descriptions
- Adjust pricing based on market
- Maintain inventory accuracy
EOT;
    }

    /**
     * Generate order sync guide content.
     */
    private function generateOrderSyncGuide(): string
    {
        return <<<EOT
# Order Synchronization Guide

## Overview
Order synchronization automatically imports eBay orders into Shopify, creating a unified order management system and enabling streamlined fulfillment processes.

## How Order Sync Works
1. **Order Detection**: System monitors eBay for new orders
2. **Data Mapping**: Maps eBay order data to Shopify format
3. **Order Creation**: Creates corresponding order in Shopify
4. **Inventory Update**: Adjusts inventory levels automatically
5. **Status Sync**: Keeps fulfillment status synchronized

## Configuration Settings

### Enable Order Sync
- Navigate to Settings > Order Sync
- Toggle "Enable Order Sync" to ON
- Set sync frequency (real-time recommended)
- Configure order import rules

### Order Mapping
- Map eBay buyer information to Shopify customers
- Configure shipping address handling
- Set up payment method mapping
- Define order status translations

### Fulfillment Settings
- Enable automatic fulfillment sync
- Configure tracking number updates
- Set up shipping carrier mapping
- Define delivery confirmation handling

## Best Practices

### Order Processing
- Process orders promptly to maintain good metrics
- Use consistent fulfillment workflows
- Update tracking information quickly
- Communicate with buyers proactively

### Inventory Management
- Ensure accurate stock levels before sync
- Monitor for overselling situations
- Set up low stock alerts
- Use buffer stock for popular items

### Customer Service
- Respond to buyer messages quickly
- Handle returns and refunds promptly
- Maintain consistent communication tone
- Use templates for common responses

## Common Issues and Solutions

### Order Import Failures
- **Cause**: Missing product mappings or invalid data
- **Solution**: Check product links, verify order details
- **Prevention**: Maintain accurate product mappings

### Inventory Conflicts
- **Cause**: Simultaneous sales on both platforms
- **Solution**: Implement buffer stock, faster sync
- **Prevention**: Use real-time inventory sync

### Address Validation Issues
- **Cause**: Invalid or incomplete shipping addresses
- **Solution**: Manual review and correction
- **Prevention**: Use address validation services

### Payment Processing
- **Cause**: Payment method not supported in Shopify
- **Solution**: Manual order processing
- **Prevention**: Configure payment method mappings

## Monitoring and Reporting

### Order Sync Dashboard
- View recent order imports
- Monitor sync success rates
- Check for failed imports
- Review processing times

### Performance Metrics
- Track order processing speed
- Monitor fulfillment accuracy
- Analyze customer satisfaction
- Review return rates

### Error Handling
- Automatic retry for temporary failures
- Detailed error logs and notifications
- Manual intervention options
- Escalation procedures for critical issues
EOT;
    }

    /**
     * Generate initial setup guide content.
     */
    private function generateInitialSetupGuide(): string
    {
        return <<<EOT
# Initial Setup and Configuration Guide

## Getting Started
This guide walks you through the initial setup of your eBay-Shopify integration, from authentication to your first product upload.

## Step 1: eBay Account Connection
1. **Install the App**: Install from Shopify App Store
2. **eBay Authentication**: Click "Connect eBay Account"
3. **Grant Permissions**: Authorize required eBay permissions
4. **Verify Connection**: Confirm successful authentication

## Step 2: Basic Configuration
1. **Site Selection**: Choose your eBay marketplace (US, UK, etc.)
2. **Currency Settings**: Set default currency for listings
3. **Business Policies**: Import existing eBay policies
4. **Sync Preferences**: Configure initial sync settings

## Step 3: Create Your First Profile
1. **Profile Setup**: Create a profile for your main product category
2. **Category Selection**: Choose appropriate eBay category
3. **Pricing Rules**: Set up pricing strategy
4. **Shipping Policy**: Select or create shipping policy

## Step 4: Product Preparation
1. **Product Review**: Ensure Shopify products are complete
2. **Image Optimization**: Verify image quality and dimensions
3. **SKU Management**: Ensure all variants have unique SKUs
4. **Inventory Check**: Confirm accurate stock levels

## Step 5: Test Upload
1. **Select Test Product**: Choose a simple product for testing
2. **Assign Profile**: Apply your created profile
3. **Upload Product**: Perform test upload
4. **Verify Listing**: Check listing on eBay for accuracy

## Step 6: Bulk Upload Setup
1. **Profile Assignment**: Create profiles for different categories
2. **Collection Mapping**: Map Shopify collections to profiles
3. **Upload Rules**: Set up automated upload rules
4. **Schedule Setup**: Configure upload schedules

## Common Setup Issues

### Authentication Problems
- **Issue**: eBay connection fails
- **Solution**: Clear browser cache, try different browser
- **Check**: Ensure eBay account is in good standing

### Permission Errors
- **Issue**: Insufficient eBay permissions
- **Solution**: Re-authenticate with full permissions
- **Check**: Verify business account requirements

### Policy Import Issues
- **Issue**: Business policies not importing
- **Solution**: Create policies in eBay Seller Hub first
- **Check**: Ensure policies are active and valid

### Category Selection Problems
- **Issue**: Can't find appropriate category
- **Solution**: Use eBay's category browser
- **Check**: Research competitor listings

## Best Practices for Setup

### Planning Phase
- Audit your Shopify catalog before setup
- Research eBay categories for your products
- Plan your profile strategy
- Set realistic timelines

### Testing Phase
- Start with a small subset of products
- Test different categories and profiles
- Verify all sync functions work correctly
- Monitor for any errors or issues

### Launch Phase
- Gradually increase upload volume
- Monitor performance metrics
- Adjust settings based on results
- Scale up successful strategies

## Post-Setup Optimization

### Performance Monitoring
- Track upload success rates
- Monitor listing performance
- Analyze sales data
- Identify optimization opportunities

### Ongoing Maintenance
- Regular profile updates
- Policy reviews and updates
- Inventory accuracy checks
- Performance optimization

### Scaling Strategies
- Expand to new categories
- Increase upload volumes
- Automate more processes
- Optimize for better performance
EOT;
    }

    /**
     * Generate troubleshooting guide content.
     */
    private function generateTroubleshootingGuide(): string
    {
        return <<<EOT
# Common Issues and Troubleshooting Guide

## Authentication Issues

### eBay Token Expired
- **Symptoms**: "Refresh token expired" errors
- **Solution**: Re-authenticate eBay account
- **Steps**: Go to Settings > eBay Connection > Reconnect
- **Prevention**: Monitor token expiration dates

### Permission Denied Errors
- **Symptoms**: API calls failing with permission errors
- **Solution**: Check eBay account permissions
- **Steps**: Verify business account status, re-grant permissions
- **Prevention**: Maintain good eBay account standing

## Product Upload Issues

### Missing Required Fields
- **Symptoms**: Upload fails with "required field missing"
- **Solution**: Complete all required product information
- **Steps**: Check category requirements, fill missing data
- **Prevention**: Use data validation before upload

### Image Upload Failures
- **Symptoms**: Products upload without images
- **Solution**: Optimize images and retry
- **Steps**: Resize images, check formats, verify URLs
- **Prevention**: Use proper image specifications

### Category Restrictions
- **Symptoms**: "Category not allowed" errors
- **Solution**: Choose appropriate category or get approval
- **Steps**: Research category requirements, apply for approval
- **Prevention**: Understand eBay category policies

## Sync Issues

### Inventory Not Syncing
- **Symptoms**: Stock levels don't match between platforms
- **Solution**: Check sync settings and run manual sync
- **Steps**: Verify sync configuration, check error logs
- **Prevention**: Monitor sync status regularly

### Order Sync Failures
- **Symptoms**: eBay orders not appearing in Shopify
- **Solution**: Check order sync settings and product mappings
- **Steps**: Verify product links, check order status
- **Prevention**: Maintain accurate product mappings

### Slow Sync Performance
- **Symptoms**: Syncs taking too long to complete
- **Solution**: Optimize sync settings and reduce batch sizes
- **Steps**: Adjust sync frequency, check API limits
- **Prevention**: Monitor performance metrics

## API and Technical Issues

### Rate Limit Exceeded
- **Symptoms**: "Rate limit exceeded" errors
- **Solution**: Reduce API call frequency
- **Steps**: Adjust sync intervals, optimize batch sizes
- **Prevention**: Monitor API usage patterns

### Server Timeouts
- **Symptoms**: Operations timing out or failing
- **Solution**: Retry operations or contact support
- **Steps**: Check server status, retry failed operations
- **Prevention**: Use appropriate timeout settings

### Data Validation Errors
- **Symptoms**: Invalid data format errors
- **Solution**: Correct data format and retry
- **Steps**: Review error messages, fix data issues
- **Prevention**: Use proper data validation

## Performance Issues

### Slow Upload Speeds
- **Symptoms**: Uploads taking longer than expected
- **Solution**: Optimize images and reduce batch sizes
- **Steps**: Compress images, adjust upload settings
- **Prevention**: Use optimal upload configurations

### High Error Rates
- **Symptoms**: Many uploads failing
- **Solution**: Review and fix common error causes
- **Steps**: Analyze error patterns, fix root causes
- **Prevention**: Implement proper quality checks

### Memory or Resource Issues
- **Symptoms**: System running slowly or crashing
- **Solution**: Optimize resource usage
- **Steps**: Reduce concurrent operations, clear cache
- **Prevention**: Monitor system resources

## Getting Help

### Self-Service Resources
- Check error logs for specific messages
- Review documentation and guides
- Use built-in diagnostic tools
- Search community forums

### Contacting Support
- Gather relevant error messages and logs
- Provide detailed description of issue
- Include steps to reproduce problem
- Specify urgency level

### Escalation Process
- Start with standard support channels
- Provide business impact information
- Request escalation if needed
- Follow up on ticket status

## Prevention Strategies

### Regular Maintenance
- Monitor system health regularly
- Update configurations as needed
- Review and optimize performance
- Stay updated with platform changes

### Best Practices
- Follow recommended configurations
- Use proper data formats
- Implement error handling
- Monitor key metrics

### Proactive Monitoring
- Set up alerts for critical issues
- Monitor API usage and limits
- Track performance trends
- Review error patterns regularly
EOT;
    }

    /**
     * Generate Shopify platform guide content.
     */
    private function generateShopifyPlatformGuide(): string
    {
        return <<<EOT
# Shopify Platform Integration Guide

## Understanding Shopify Data Structure

### Products and Variants
- **Products**: Main product entity with title, description, images
- **Variants**: Different versions (size, color, style) of the same product
- **SKUs**: Unique identifiers for each variant
- **Inventory**: Stock levels tracked per variant

### Collections
- **Smart Collections**: Auto-populated based on rules
- **Custom Collections**: Manually curated product groups
- **Collection Rules**: Conditions for automatic inclusion
- **Collection Hierarchy**: Nested organization structure

### Product Attributes
- **Product Type**: Category classification
- **Vendor**: Brand or manufacturer
- **Tags**: Flexible labeling system
- **Metafields**: Custom data fields for additional information

## Shopify API Integration

### GraphQL API
- **Bulk Operations**: Efficient for large data sets
- **Real-time Queries**: For immediate data needs
- **Webhooks**: Event-driven updates
- **Rate Limiting**: 1000 points per app per store per minute

### REST Admin API
- **CRUD Operations**: Create, read, update, delete resources
- **Pagination**: Handle large result sets
- **Filtering**: Query specific data subsets
- **Versioning**: API version management

### Webhooks
- **Product Updates**: Real-time product change notifications
- **Inventory Updates**: Stock level change events
- **Order Events**: New orders, updates, cancellations
- **Collection Changes**: Collection modification events

## Data Mapping Best Practices

### Product Information
- **Title Mapping**: Shopify title to eBay listing title
- **Description**: HTML to eBay-compatible format
- **Images**: URL mapping and optimization
- **Pricing**: Currency conversion and markup rules

### Variant Handling
- **Size Variations**: Map to eBay item specifics
- **Color Variations**: Handle color naming differences
- **Style Variations**: Custom attribute mapping
- **SKU Management**: Maintain unique identifiers

### Inventory Tracking
- **Stock Levels**: Real-time synchronization
- **Inventory Policies**: Continue selling when out of stock
- **Fulfillment Services**: Third-party inventory management
- **Location-based Inventory**: Multi-location stock tracking

## Common Shopify Challenges

### Variant Limitations
- **100 Variant Limit**: Shopify's maximum variants per product
- **3 Option Limit**: Maximum of 3 variant options (size, color, style)
- **Workarounds**: Split products or use metafields

### Image Management
- **Image Limits**: 250 images per product
- **Alt Text**: SEO and accessibility considerations
- **Image Optimization**: Size and format optimization
- **CDN Usage**: Shopify's content delivery network

### Metafields
- **Custom Data**: Store additional product information
- **Namespace Organization**: Logical grouping of metafields
- **Data Types**: Text, number, boolean, JSON
- **API Access**: Reading and writing metafield data

## Integration Optimization

### Performance
- **Bulk Operations**: Use for large data sets
- **Caching**: Store frequently accessed data
- **Pagination**: Handle large result sets efficiently
- **Error Handling**: Robust retry mechanisms

### Data Quality
- **Validation**: Ensure data integrity
- **Standardization**: Consistent data formats
- **Completeness**: Required field validation
- **Accuracy**: Regular data audits

### Scalability
- **Rate Limit Management**: Respect API limits
- **Queue Management**: Handle high-volume operations
- **Resource Optimization**: Efficient memory and CPU usage
- **Monitoring**: Track performance metrics

## Shopify Plus Features

### Flow Automation
- **Trigger Events**: Product updates, inventory changes
- **Conditions**: Logic-based decision making
- **Actions**: Automated responses to events
- **Integration**: Connect with external systems

### Scripts
- **Line Item Scripts**: Modify cart contents
- **Shipping Scripts**: Custom shipping calculations
- **Payment Scripts**: Payment method modifications
- **Discount Scripts**: Custom discount logic

### Advanced APIs
- **GraphQL Admin API**: Enhanced query capabilities
- **Storefront API**: Customer-facing data access
- **Partner API**: App and store management
- **Events API**: Real-time event streaming

## Best Practices

### Development
- **API Versioning**: Use stable API versions
- **Error Handling**: Comprehensive error management
- **Testing**: Thorough testing in development stores
- **Documentation**: Maintain clear documentation

### Security
- **Authentication**: Secure API access
- **Data Protection**: Encrypt sensitive data
- **Access Control**: Limit permissions appropriately
- **Audit Logging**: Track data access and changes

### Maintenance
- **Regular Updates**: Keep integrations current
- **Performance Monitoring**: Track key metrics
- **Error Monitoring**: Proactive issue detection
- **User Feedback**: Continuous improvement based on feedback
EOT;
    }

    /**
     * Generate eBay platform guide content.
     */
    private function generateEbayPlatformGuide(): string
    {
        return <<<EOT
# eBay Platform Integration Guide

## eBay Marketplace Structure

### Site-Specific Considerations
- **eBay US (Site ID 0)**: Largest marketplace, USD currency
- **eBay UK (Site ID 3)**: GBP currency, different regulations
- **eBay Germany (Site ID 77)**: EUR currency, strict compliance
- **eBay Australia (Site ID 15)**: AUD currency, unique categories

### Category Hierarchy
- **Level 1**: Broad categories (Electronics, Fashion, etc.)
- **Level 2**: Subcategories (Cell Phones, Women's Clothing)
- **Level 3**: Specific categories (Smartphones, Dresses)
- **Category IDs**: Numeric identifiers for API calls

### Listing Formats
- **Fixed Price**: Buy It Now listings
- **Auction**: Traditional bidding format
- **Best Offer**: Allow buyer negotiations
- **Store Inventory**: Lower-cost listing format

## eBay API Integration

### Trading API
- **AddFixedPriceItem**: Create new listings
- **ReviseFixedPriceItem**: Update existing listings
- **EndFixedPriceItem**: End listings early
- **GetSellerList**: Retrieve seller's listings

### Inventory API
- **Bulk Operations**: Manage large inventories
- **Offer Management**: Create and manage offers
- **Inventory Location**: Multi-location inventory
- **Compatibility**: Product compatibility data

### Fulfillment API
- **Order Management**: Retrieve and manage orders
- **Shipping Labels**: Generate shipping labels
- **Tracking Updates**: Update tracking information
- **Return Management**: Handle returns and refunds

## Business Policies

### Shipping Policies
- **Domestic Shipping**: Within-country shipping options
- **International Shipping**: Cross-border shipping
- **Handling Time**: Days to ship after payment
- **Shipping Costs**: Fixed or calculated rates

### Return Policies
- **Return Period**: Days buyer has to return
- **Return Shipping**: Who pays return shipping
- **Restocking Fee**: Fee for returned items
- **Return Conditions**: Acceptable return reasons

### Payment Policies
- **Accepted Methods**: PayPal, managed payments
- **Payment Terms**: Immediate or delayed payment
- **Deposit Requirements**: For high-value items
- **Payment Instructions**: Special payment notes

## Listing Requirements

### Required Information
- **Title**: Descriptive, keyword-rich (80 characters max)
- **Category**: Appropriate eBay category
- **Condition**: Item condition (New, Used, etc.)
- **Price**: Competitive pricing
- **Images**: High-quality product photos

### Item Specifics
- **Brand**: Manufacturer or brand name
- **Model**: Specific model number
- **Color**: Primary color
- **Size**: Dimensions or clothing size
- **Material**: What item is made from

### Compliance Requirements
- **Prohibited Items**: Items not allowed on eBay
- **Restricted Categories**: Categories requiring approval
- **Intellectual Property**: Avoid trademark violations
- **Safety Standards**: Meet applicable safety requirements

## eBay Fees Structure

### Insertion Fees
- **Free Listings**: Monthly allocation of free listings
- **Additional Listings**: Fees for exceeding free allocation
- **Store Subscriptions**: Higher free listing limits
- **Category Variations**: Some categories have different fees

### Final Value Fees
- **Standard Rate**: Percentage of final sale price
- **Category-Specific**: Different rates for different categories
- **International Sales**: Additional fees for cross-border
- **Payment Processing**: Fees for managed payments

### Optional Fees
- **Listing Upgrades**: Bold, highlight, gallery plus
- **Promoted Listings**: Advertising fees
- **Store Subscriptions**: Monthly store fees
- **Additional Categories**: Listing in multiple categories

## Performance Standards

### Seller Standards
- **Defect Rate**: Keep below 2% for Top Rated status
- **Late Shipment Rate**: Ship within handling time
- **Valid Tracking Rate**: Provide tracking for shipments
- **Return Rate**: Minimize return requests

### Search Ranking Factors
- **Best Match Algorithm**: eBay's search ranking system
- **Listing Quality**: Complete, accurate listings
- **Seller Performance**: Metrics affect visibility
- **Price Competitiveness**: Competitive pricing helps ranking

### Customer Service
- **Response Time**: Respond to messages within 24 hours
- **Resolution Rate**: Resolve issues satisfactorily
- **Communication**: Professional, helpful responses
- **Proactive Updates**: Keep buyers informed

## Advanced Features

### eBay Stores
- **Store Categories**: Organize your inventory
- **Custom Pages**: About page, policies, etc.
- **Store Search**: Help buyers find your items
- **Promotional Tools**: Sales, markdowns, promotions

### Promoted Listings
- **Ad Campaigns**: Increase listing visibility
- **Keyword Targeting**: Target specific search terms
- **Bid Management**: Set competitive ad rates
- **Performance Tracking**: Monitor ad effectiveness

### International Selling
- **Global Shipping Program**: eBay-managed international shipping
- **Direct International**: Ship internationally yourself
- **Currency Conversion**: Handle multiple currencies
- **Customs Documentation**: Required for international shipments

## Best Practices

### Listing Optimization
- **SEO-Friendly Titles**: Include relevant keywords
- **High-Quality Images**: Multiple angles, good lighting
- **Detailed Descriptions**: Comprehensive product information
- **Competitive Pricing**: Research market prices

### Inventory Management
- **Accurate Stock Levels**: Prevent overselling
- **Regular Updates**: Keep listings current
- **Seasonal Adjustments**: Adapt to market changes
- **Performance Monitoring**: Track listing performance

### Customer Relations
- **Fast Shipping**: Meet or exceed handling times
- **Good Communication**: Respond promptly and professionally
- **Quality Products**: Ensure items match descriptions
- **Easy Returns**: Make return process simple
EOT;
    }
}
