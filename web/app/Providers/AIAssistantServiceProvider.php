<?php

namespace App\Providers;

use App\Services\AI\AIAssistantService;
use App\Services\AI\Knowledge\DocumentProcessor;
use App\Services\AI\Knowledge\KnowledgeIngestionService;
use App\Services\AI\LLM\OpenAIService;
use App\Services\AI\VectorStore\PineconeService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class AIAssistantServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register OpenAI Service
        $this->app->singleton(OpenAIService::class, function ($app) {
            return new OpenAIService();
        });

        // Register Pinecone Service
        $this->app->singleton(PineconeService::class, function ($app) {
            return new PineconeService();
        });

        // Register Document Processor
        $this->app->singleton(DocumentProcessor::class, function ($app) {
            return new DocumentProcessor(
                $app->make(OpenAIService::class),
                $app->make(PineconeService::class)
            );
        });

        // Register Knowledge Ingestion Service
        $this->app->singleton(KnowledgeIngestionService::class, function ($app) {
            return new KnowledgeIngestionService(
                $app->make(DocumentProcessor::class)
            );
        });

        // Register AI Assistant Service
        $this->app->singleton(AIAssistantService::class, function ($app) {
            return new AIAssistantService(
                $app->make(OpenAIService::class),
                $app->make(PineconeService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\IngestKnowledgeBase::class,
                \App\Console\Commands\TestAIServices::class,
                \App\Console\Commands\TestOpenAIModels::class,
                \App\Console\Commands\TestAIConversation::class,
            ]);
        }

        // Validate configuration on boot
        $this->validateConfiguration();
    }

    /**
     * Validate AI Assistant configuration.
     */
    private function validateConfiguration(): void
    {
        $requiredConfigs = [
            'ai_assistant.openai.api_key' => 'OPENAI_API_KEY',
            'ai_assistant.pinecone.api_key' => 'PINECONE_API_KEY',
        ];

        foreach ($requiredConfigs as $configKey => $envKey) {
            if (empty(config($configKey))) {
                Log::warning("AI Assistant configuration missing: {$envKey}");
            }
        }

        // Validate Pinecone connection method (either host or environment)
        $pineconeHost = config('ai_assistant.pinecone.host');
        $pineconeEnvironment = config('ai_assistant.pinecone.environment');

        if (empty($pineconeHost) && empty($pineconeEnvironment)) {
            Log::warning("AI Assistant configuration missing: Either PINECONE_HOST or PINECONE_ENVIRONMENT must be set");
        }
    }
}
