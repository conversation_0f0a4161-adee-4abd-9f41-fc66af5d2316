<?php

namespace App\Console\Commands;

use App\Models\Session;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use JsonException;
use Psr\Http\Client\ClientExceptionInterface;
use Shopify\Clients\Graphql;
use Shopify\Exception\MissingArgumentException;
use Shopify\Exception\UninitializedContextException;

class UpdateClosedStore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:closed-store';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checks if the store is closed and updates the store';


    /**
     * Execute the console command.
     *
     * @return void
     * @throws ClientExceptionInterface
     * @throws JsonException
     * @throws MissingArgumentException
     * @throws UninitializedContextException
     */
    public function handle(): void
    {
        $sessions = Session::whereNotNull('access_token')
        ->whereHas('ebayUser')
        ->where('enable_sync', 1)
        ->get();
        foreach ($sessions as $session) {
            $client = new Graphql($session->shop, $session->access_token);
            $result = $client->query(
                'query {
                    shop {
                        plan {
                            displayName
                        }
                    }
                }'
            );
            $errorMessage = Arr::get($result->getDecodedBody(), 'errors');
            echo $errorMessage;
            if ($errorMessage === 'Not Found' || $errorMessage === 'Invalid API key or access token' || $errorMessage === 'Unavailable Shop') {
                Log::channel('daily')->info('Store is closed', [
                    'shopName' => $session->shop
                ]);
                $session->update(['enable_sync' => 0]);
            }
        }
    }
}
