<?php

namespace App\Console\Commands;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Jobs\Ebay\FetchShippingServiceDetails;
use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Services\OAuth\EbayOAuthService;
use App\Module\Ebay\Services\User\UserMarketplaceIdService;
use App\Module\Ebay\Services\User\UserSiteIdService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class SyncEbayShippingServiceDetails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-shipping-service-details';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The command synchronizes the shipping service details from ebay';


    /**
     * Execute the console command.
     *
     * @param EbayOAuthService $ebayOAuthService
     * @return void
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public function handle(
        EbayOAuthService $ebayOAuthService
    ): void {
        $ebayUser = $this->getEbayUser();

        if (!$ebayUser) {
            $this->alert('No ebay user found');
            return;
        }

        $accessToken = $ebayOAuthService->getAccessToken($ebayUser);

        $siteIds = UserSiteIdService::ALL_SITE_IDS;
        $marketPlaceIds = UserMarketplaceIdService::MARKETPLACE_IDS;

        $jobs = [];
        foreach ($siteIds as $country => $siteId) {
            $marketPlaceId = Arr::get($marketPlaceIds, $country);

            if ($marketPlaceId === null) {
                Log::channel('daily')->info("No market place id found for ($siteId) $country");
                continue;
            }

            $jobs[] = new FetchShippingServiceDetails($marketPlaceId, $country, $accessToken);
        }

        Bus::chain($jobs)
            ->onQueue('fetch_ebay_shipping_service_details')
            ->dispatch();

        $this->info("Dispatched " . count($jobs) . " jobs to fetch shipping service details");
    }

    private function getEbayUser(): ?EbayUser
    {
        if (App::isProduction()) {
            $ebayUser = EbayUser::query()
                ->whereIn('ebay_user_name', EbayUser::PRODUCTION_USER_NAMES)
                ->first();
        } else {
            $ebayUser = EbayUser::query()->first();
        }

        return $ebayUser;
    }
}
