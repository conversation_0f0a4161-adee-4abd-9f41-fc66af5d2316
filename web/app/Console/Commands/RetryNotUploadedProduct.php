<?php

namespace App\Console\Commands;

use App\Jobs\UploadOrUpdateProductToEbayJob;
use App\Models\ShopifyProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class RetryNotUploadedProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload:not-uploaded';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $products = ShopifyProduct::where('upload_status', 0)->where('profile_id', '>', 0)->get();
        foreach ($products as $product) {
            $jobs[] = new UploadOrUpdateProductToEbayJob(
                $product->id,
                $product->session_id,
            );
        }
        Bus::chain($jobs)
            ->onQueue('product_upload_to_ebay')
            ->dispatch();
    }
}
