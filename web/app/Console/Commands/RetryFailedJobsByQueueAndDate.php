<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;

class RetryFailedJobsByQueueAndDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'retry:failed-jobs {queue : The name of the queue}
    {--date= : The date for filtering failed jobs (format: Y-m-d)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed jobs for a specific queue and an optional date';


    public function handle()
    {
        $queue = $this->argument('queue');
        $date = $this->option('date');

        // Validate queue name
        if (!is_string($queue) || empty($queue)) {
            $this->error('Invalid queue name provided.');
            return;
        }

        // Validate date format
        if ($date && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            $this->error('Invalid date format. Please use the format Y-m-d.');
            return;
        }

        // If date is not provided, use today's date
        $date = $date ?? now()->format('Y-m-d');

        $failedJobs = DB::table('failed_jobs')
            ->where('queue', $queue)
            ->whereDate('failed_at', $date)
            ->get();

        if ($failedJobs->isEmpty()) {
            $this->info('No failed jobs found for queue "' . $queue . '" on ' . $date . '.');
        } else {
            foreach ($failedJobs as $failedJob) {
                Queue::pushRaw($failedJob->payload, $queue);
                DB::table('failed_jobs')->where('id', $failedJob->id)->delete();
            }

            $this->info('Failed jobs for queue "' . $queue . '" retried for ' . $date . '.');
            $this->info('Retry process completed.');
        }
    }
}
