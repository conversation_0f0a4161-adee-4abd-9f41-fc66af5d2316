<?php

namespace App\Console\Commands;

use App\Jobs\User\FetchUserDetails;
use App\Models\Ebay\EbayUser;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class SyncEbayUserDetails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $users = EbayUser::with('userSetting')
            ->where('refresh_token_expired', 0)
            ->whereHas('session', function (Builder $query) {
                return $query->where('enable_sync', true);
            })->get();
        foreach ($users as $ebayUser) {
            FetchUserDetails::dispatch($ebayUser)->onQueue('user_details');
        }
    }
}
