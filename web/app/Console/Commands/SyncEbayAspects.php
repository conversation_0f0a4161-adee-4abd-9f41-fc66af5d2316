<?php

namespace App\Console\Commands;

use App\Jobs\Ebay\FetchItemAspectForCategory;
use App\Models\EbayCategories;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SyncEbayAspects extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-aspects {marketplace_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(string $marketplace_id = '')
    {
        $categories = EbayCategories::query();
        if($this->argument('marketplace_id')) {
            $categories->where('marketplace_id', $this->argument('marketplace_id'));
        }
        $categories->chunk(50, function ($categories) {
            FetchItemAspectForCategory::dispatch($categories)->onQueue('ebay_categories');
        });
    }
}
