<?php

namespace App\Console\Commands;

use App\Models\Session;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use JsonException;
use Psr\Http\Client\ClientExceptionInterface;
use Shopify\Clients\Graphql;
use Shopify\Exception\MissingArgumentException;
use Shopify\Exception\UninitializedContextException;

class UpdateSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:store-subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the store subscription';


    /**
     * Execute the console command.
     *
     * @return void
     * @throws ClientExceptionInterface
     * @throws JsonException
     * @throws MissingArgumentException
     * @throws UninitializedContextException
     */
    public function handle(): void
    {
        $sessions = Session::whereNotNull('access_token')
        ->where('enable_sync', 1)
        ->get();
        foreach ($sessions as $session) {
            $client = new Graphql($session->shop, $session->access_token);
            $result = $client->query(
                'query GetRecurringApplicationCharges {
                    currentAppInstallation {
                        activeSubscriptions {
                        id
                        name
                        status
                        
                        }
                    }
                }'
            );
            $activeSubscriptionId = Arr::get($result->getDecodedBody(), 'data.currentAppInstallation.activeSubscriptions.0.id');
            echo $activeSubscriptionId;
            if ($activeSubscriptionId) {
                $session->update(['app_subscription_id' => $activeSubscriptionId]);
            }
        }
    }
}
