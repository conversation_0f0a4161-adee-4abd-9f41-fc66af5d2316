<?php

namespace App\Console\Commands;

use App\Jobs\Ebay\FetchNewEbayProducts;
use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\Product\EbayProductService;
use DateTime;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Bus;

class SyncEbayProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync eBay product to our system';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(EbayProductService $service)
    {
        $users = EbayUser::with('userSetting')
            ->where('refresh_token_expired', 0)
            ->where('is_ebay_products_fetching', 0)
            ->whereHas('session', function (Builder $query) {
                return $query->where('enable_sync', true);
            })->get();
        $jobs = [];
        foreach ($users as $ebayUser) {
            $start_date = new DateTime($ebayUser->last_product_fetched_at ?? $ebayUser->registration_date);
            $reqArgs = array(
                'start_time' => $start_date,
                'end_time' => Helper::getEndTime($start_date),
                'page_number' => 1
            );
            $jobs[] = new FetchNewEbayProducts($ebayUser->id, $reqArgs);
            $ebayUser->update(['last_product_fetched_at' => $reqArgs['end_time']]);
        }
        Bus::batch($jobs)
            ->onQueue('products_queue_periodic')
            ->dispatch();
    }
}
