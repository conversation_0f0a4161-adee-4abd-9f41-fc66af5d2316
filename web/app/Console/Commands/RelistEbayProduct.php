<?php

namespace App\Console\Commands;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Models\Ebay\EbayUser;
use App\Models\ShopifyProduct;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\Product\Builder\RelistEbayItemRequestBuilder;
use App\Module\Ebay\Services\Product\Formatter\EbayRelistItemRequestFormatter;
use DTS\eBaySDK\Trading\Services\TradingService;
use DTS\eBaySDK\Trading\Types\RelistFixedPriceItemRequestType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RelistEbayProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'relist:ebay-product {ebayUserId} {shopifyProductId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Relist ebay product from shopify product id for testing purpose only';


    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public function handle(
        EbayRelistItemRequestFormatter $ebayRelistItemRequestFormatter,
        RelistEbayItemRequestBuilder $relistEbayItemRequestBuilder
    ): void {
        $ebayUserId = $this->argument('ebayUserId');
        $shopifyProductId = $this->argument('shopifyProductId');

        /** @var ShopifyProduct $shopifyProduct */
        $shopifyProduct = ShopifyProduct::query()->where('shopify_product_id', $shopifyProductId)->first();

        /** @var EbayUser $ebayUser */
        $ebayUser = EbayUser::query()->find($ebayUserId);

        $formattedData = $ebayRelistItemRequestFormatter->format($ebayUser, $shopifyProduct);
        $itemRequest = $relistEbayItemRequestBuilder->build($formattedData, $ebayUser);
        $service = new TradingService(Helper::eBayCredentials($ebayUser));
        $request = new RelistFixedPriceItemRequestType();
        $request->Item = $itemRequest;
        $response = $service->relistFixedPriceItem($request);

        Log::channel('daily')->info('Command : Item being relisted ', [
            'formattedData' => $formattedData,
            'ebayUserId' => $ebayUser->id,
            'itemID' => $itemRequest->ItemID,
            'shopifyProductId' => $shopifyProduct->id,
            'response' => $response
        ]);

        if (isset($response->Errors)) {
            $errorMessage = json_encode($response->Errors);
            $this->error("Error occurred while relisting : $errorMessage");
            return;
        }
        $relistedItemId = $response->ItemID;
        $this->info(
            "Successfully relisted the ended ebay product , RelistedItemId : $relistedItemId "
        );
    }
}
