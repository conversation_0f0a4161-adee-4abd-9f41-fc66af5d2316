<?php

namespace App\Console\Commands\EPS;

use App\Module\Ebay\Services\EPS\EPSReportingService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class GenerateEPSReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ebay:eps-report
                            {--user-id= : Specific eBay user ID to report on}
                            {--product-id= : Specific product ID to report on}
                            {--days=7 : Number of days to include in the report}
                            {--format=console : Output format (console, json, csv)}
                            {--output= : Path to save the report file (only for json/csv formats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a report of eBay EPS image upload statistics';

    /**
     * Execute the console command.
     *
     * @param EPSReportingService $reportingService
     * @return int
     */
    public function handle(EPSReportingService $reportingService): int
    {
        $userId = $this->option('user-id');
        $productId = $this->option('product-id');
        $days = (int) $this->option('days');
        $format = $this->option('format');
        $outputPath = $this->option('output');
        
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        
        $this->info("Generating EPS upload report" . 
            ($userId ? " for eBay user ID: $userId" : "") . 
            ($productId ? " and product ID: $productId" : "") . 
            " for the last $days days"
        );
        
        // Get report data
        $summary = $reportingService->getUploadSummary(
            $userId ? (int) $userId : null,
            $productId ? (int) $productId : null,
            $startDate
        );
        
        $dailyStats = $reportingService->getDailyStats(
            $userId ? (int) $userId : null,
            $productId ? (int) $productId : null,
            $days
        );
        
        $report = [
            'generated_at' => Carbon::now()->toDateTimeString(),
            'period' => [
                'start' => $startDate->toDateTimeString(),
                'end' => Carbon::now()->toDateTimeString(),
                'days' => $days,
            ],
            'filters' => [
                'ebay_user_id' => $userId,
                'product_id' => $productId,
            ],
            'summary' => $summary,
            'daily_stats' => $dailyStats,
        ];
        
        // Output report in requested format
        if ($format === 'json') {
            $this->outputJson($report, $outputPath);
        } elseif ($format === 'csv') {
            $this->outputCsv($dailyStats, $outputPath);
        } else {
            $this->outputConsole($report);
        }
        
        return 0;
    }
    
    /**
     * Output report to console.
     *
     * @param array $report
     * @return void
     */
    protected function outputConsole(array $report): void
    {
        $this->info('EPS Upload Report');
        $this->info('==================');
        $this->info("Generated at: {$report['generated_at']}");
        $this->info("Period: {$report['period']['start']} - {$report['period']['end']} ({$report['period']['days']} days)");
        
        if ($report['filters']['ebay_user_id']) {
            $this->info("eBay User ID: {$report['filters']['ebay_user_id']}");
        }
        
        if ($report['filters']['product_id']) {
            $this->info("Product ID: {$report['filters']['product_id']}");
        }
        
        $this->info("\nSummary Statistics:");
        $this->info("-----------------");
        $this->info("Total uploads: {$report['summary']['total']}");
        $this->info("Success rate: {$report['summary']['success_rate']}%");
        $this->info("Average processing time: {$report['summary']['avg_processing_seconds']} seconds");
        
        $this->info("\nStatus Breakdown:");
        foreach ($report['summary']['status_counts'] as $status => $count) {
            $percentage = $report['summary']['total'] > 0 
                ? round(($count / $report['summary']['total']) * 100, 2) 
                : 0;
            $this->info("- $status: $count ($percentage%)");
        }
        
        $this->info("\nTop Failure Reasons:");
        if (count($report['summary']['top_failures']) > 0) {
            foreach ($report['summary']['top_failures'] as $failure) {
                $this->info("- {$failure['message']} ({$failure['count']} occurrences)");
            }
        } else {
            $this->info("- No failures recorded");
        }
        
        $this->info("\nCurrent Queue Status:");
        $this->info("- Pending: {$report['summary']['queue_status']['pending']}");
        $this->info("- Processing: {$report['summary']['queue_status']['processing']}");
        $this->info("- Scheduled for retry: {$report['summary']['queue_status']['retry_scheduled']}");
        
        if ($report['summary']['queue_status']['oldest_pending']) {
            $oldest = $report['summary']['queue_status']['oldest_pending'];
            $this->info("- Oldest pending upload: {$oldest['created_at']} ({$oldest['age_hours']} hours old)");
        }
        
        $this->info("\nDaily Statistics:");
        $this->info("-----------------");
        $this->table(
            ['Date', 'Total', 'Completed', 'Failed', 'Success Rate'],
            collect($report['daily_stats'])->map(function ($day) {
                return [
                    $day['date'],
                    $day['total'],
                    $day['completed'],
                    $day['failed'],
                    $day['success_rate'] . '%',
                ];
            })
        );
    }
    
    /**
     * Output report to JSON file.
     *
     * @param array $report
     * @param string|null $outputPath
     * @return void
     */
    protected function outputJson(array $report, ?string $outputPath): void
    {
        $json = json_encode($report, JSON_PRETTY_PRINT);
        
        if ($outputPath) {
            file_put_contents($outputPath, $json);
            $this->info("Report saved to: $outputPath");
        } else {
            $this->line($json);
        }
    }
    
    /**
     * Output daily stats to CSV file.
     *
     * @param array $dailyStats
     * @param string|null $outputPath
     * @return void
     */
    protected function outputCsv(array $dailyStats, ?string $outputPath): void
    {
        $csv = "Date,Total,Completed,Failed,Success Rate\n";
        
        foreach ($dailyStats as $day) {
            $csv .= "{$day['date']},{$day['total']},{$day['completed']},{$day['failed']},{$day['success_rate']}%\n";
        }
        
        if ($outputPath) {
            file_put_contents($outputPath, $csv);
            $this->info("CSV report saved to: $outputPath");
        } else {
            $this->line($csv);
        }
    }
} 