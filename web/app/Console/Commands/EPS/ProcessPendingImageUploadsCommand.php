<?php

namespace App\Console\Commands\EPS;

use App\Jobs\Ebay\EPS\BatchProcessingManager;
use App\Jobs\Ebay\EPS\ProcessBatchImageUploadsJob;
use App\Jobs\Ebay\EPS\RetryFailedImageUploadsJob;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EPS\ProductImageMapping;
use App\Module\Ebay\Repositories\EPS\ProductImageMappingRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessPendingImageUploadsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ebay:process-eps-uploads 
                            {--user-id= : Specific eBay user ID to process} 
                            {--product-id= : Specific product ID to process} 
                            {--batch-size=10 : Number of images to process per batch} 
                            {--reset-failed : Reset failed uploads to pending status}
                            {--retry-failed : Process failed uploads that are eligible for retry}
                            {--max-parallel=5 : Maximum number of parallel jobs to run}
                            {--optimize : Use optimized batch processing with dynamic scaling}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending image uploads to eBay EPS';

    /**
     * Execute the console command.
     *
     * @param ProductImageMappingRepository $repository
     * @return int
     */
    public function handle(ProductImageMappingRepository $repository): int
    {
        $userId = $this->option('user-id');
        $productId = $this->option('product-id');
        $batchSize = (int) $this->option('batch-size');
        $resetFailed = $this->option('reset-failed');
        $retryFailed = $this->option('retry-failed');

        // If resetting failed uploads
        if ($resetFailed) {
            $this->resetFailedUploads($repository, $userId, $productId);
        }

        // If processing retry-eligible failed uploads
        if ($retryFailed) {
            $this->processFailedUploads($userId, $productId, $batchSize);
        }

        // Process pending uploads for all users or specific user
        if ($userId) {
            $this->processForUser($userId, $productId, $batchSize);
        } else {
            $this->processForAllUsers($batchSize);
        }

        return 0;
    }

    /**
     * Process uploads for a specific eBay user.
     *
     * @param int $userId
     * @param int|null $productId
     * @param int $batchSize
     * @return void
     */
    protected function processForUser(int $userId, ?int $productId, int $batchSize): void
    {
        $ebayUser = EbayUser::find($userId);
        if (!$ebayUser) {
            $this->error("eBay user with ID {$userId} not found.");
            return;
        }

        $useOptimized = $this->option('optimize');
        
        if ($useOptimized) {
            $maxParallel = (int) $this->option('max-parallel');
            $this->info("Dispatching optimized batch processing for eBay user {$userId}".($productId ? " and product {$productId}" : ''));
            BatchProcessingManager::dispatch($userId, $productId, $maxParallel, $batchSize);
        } else {
            $this->info("Dispatching regular batch upload job for eBay user {$userId}".($productId ? " and product {$productId}" : ''));
            ProcessBatchImageUploadsJob::dispatch($userId, $productId, $batchSize);
        }
    }

    /**
     * Process uploads for all eBay users.
     *
     * @param int $batchSize
     * @return void
     */
    protected function processForAllUsers(int $batchSize): void
    {
        $ebayUsers = EbayUser::all();
        $this->info("Processing uploads for {$ebayUsers->count()} eBay users");

        foreach ($ebayUsers as $ebayUser) {
            $this->info("Dispatching batch upload job for eBay user {$ebayUser->id}");
            ProcessBatchImageUploadsJob::dispatch($ebayUser->id, null, $batchSize);
        }
    }

    /**
     * Reset failed uploads to pending status.
     *
     * @param ProductImageMappingRepository $repository
     * @param int|null $userId
     * @param int|null $productId
     * @return void
     */
    protected function resetFailedUploads(ProductImageMappingRepository $repository, ?int $userId, ?int $productId): void
    {
        $query = ProductImageMapping::query()
            ->whereIn('status', ['error', 'failed']);
            
        if ($userId) {
            $query->where('ebay_user_id', $userId);
        }
        
        if ($productId) {
            $query->where('product_id', $productId);
        }
        
        $count = $query->count();
        
        if ($count > 0) {
            $query->update([
                'status' => 'pending',
                'attempts' => 0,
                'error_message' => null,
                'retry_after' => null,
                'last_updated' => now(),
            ]);
            
            $this->info("Reset {$count} failed uploads to pending status.");
            Log::channel('eps')->info("Reset {$count} failed uploads to pending status", [
                'user_id' => $userId,
                'product_id' => $productId,
            ]);
        } else {
            $this->info("No failed uploads found to reset.");
        }
    }

    /**
     * Process failed uploads that are eligible for retry.
     *
     * @param int|null $userId
     * @param int|null $productId
     * @param int $batchSize
     * @return void
     */
    protected function processFailedUploads(?int $userId, ?int $productId, int $batchSize): void
    {
        if ($userId) {
            $this->info("Dispatching retry job for eBay user {$userId}".($productId ? " and product {$productId}" : ''));
            RetryFailedImageUploadsJob::dispatch($userId, $productId, $batchSize);
        } else {
            $ebayUsers = EbayUser::all();
            $this->info("Processing retries for {$ebayUsers->count()} eBay users");
            
            foreach ($ebayUsers as $ebayUser) {
                $this->info("Dispatching retry job for eBay user {$ebayUser->id}");
                RetryFailedImageUploadsJob::dispatch($ebayUser->id, null, $batchSize);
            }
        }
    }
} 