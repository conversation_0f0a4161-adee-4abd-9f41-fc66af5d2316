<?php

namespace App\Console\Commands;

use App\Jobs\Shopify\SyncDefaultLocationJob;
use App\Models\Session;
use Illuminate\Console\Command;

class SyncDefaultLocation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:shopify-default-location';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $sessions = Session::where('enable_sync', true)->get();
        foreach ($sessions as $session) {
            SyncDefaultLocationJob::dispatch($session);
        }
    }
}
