<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\InteractsWithTime;

class RestartSqsStandardQueuePoll extends Command
{
    use InteractsWithTime;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sqs-standard-poll:restart';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Restart sqs standard queue polling command workers';


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            Cache::forever('sqs:standard:poll:restart', $this->currentTime());

            // Send a signal to stop all processes running the sqs-standard:poll command
            $this->info('Broadcasting sqs-standard:poll stop signal to all running processes...');
        } catch (Exception $exception) {
            $this->error('Failed to send stop signals to sqs-standard:poll processes: ' . $exception->getMessage());
        }
    }
}
