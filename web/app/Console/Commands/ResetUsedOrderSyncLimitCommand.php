<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\Order\DispatchFailedOrdersToSyncJob;
use App\Models\SessionLimit;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ResetUsedOrderSyncLimitCommand extends Command
{
    protected $signature = 'reset:order-sync-limit';

    protected $description = 'Watcher to reset the order sync limit on every 1st day of the month';

    public function handle(): void
    {
        $limit = 200;
        $totalProcessed = 0;

        SessionLimit::query()->chunkById($limit, function ($sessionLimits) use (&$totalProcessed) {
            foreach ($sessionLimits as $sessionLimit) {
                $this->handleUpdate($sessionLimit);
            }

            $totalProcessed += $sessionLimits->count();
            $this->info("Reset order sync limit {$totalProcessed} records processed so far...");
        });

        $this->info("Order sync limit reset completed for all records.");
    }

    private function handleUpdate(SessionLimit $sessionLimit): void
    {
        $sessionId = $sessionLimit->session_id;
        $newResetTime = Carbon::now()->toIso8601String();

        Log::channel('daily')->info('ResetUsedOrderSyncLimit Success', [
            'Session Id' => $sessionId,
            'Last Reset Time' => $sessionLimit->last_order_sync_limit_reset_at,
            'New Reset Time' => $newResetTime,
        ]);

        $sessionLimit->update([
            'used_order_sync_limit' => 0,
            'last_order_sync_limit_reset_at' => $newResetTime,
            'last_order_sync_fail_mailed_at' => null,
            'last_order_sync_warning_mailed_at' => null
        ]);

        Log::channel("daily")->info("Dispatched jobs to sync failed orders due to monthly limit for shop $sessionId");

        DispatchFailedOrdersToSyncJob::dispatch(sessionId: $sessionId)->onQueue('order_sync_shopify');
    }
}
