<?php

namespace App\Console\Commands;

use App\Models\AI\AIConversation;
use App\Services\AI\AIAssistantService;
use Illuminate\Console\Command;

class TestAIConversation extends Command
{
    protected $signature = 'ai:test-conversation {--conversation_id=} {--interactive}';
    protected $description = 'Test AI assistant conversations from the command line';

    private AIAssistantService $aiAssistantService;

    public function __construct(AIAssistantService $aiAssistantService)
    {
        parent::__construct();
        $this->aiAssistantService = $aiAssistantService;
    }

    public function handle(): int
    {
        $this->info('🤖 AI Assistant Conversation Tester');

        // Get or create conversation
        $conversationId = $this->option('conversation_id');
        $conversation = null;

        if ($conversationId) {
            $conversation = AIConversation::find($conversationId);
            if (!$conversation) {
                $this->error("Conversation with ID {$conversationId} not found");
                return Command::FAILURE;
            }
            $this->info("Using existing conversation: {$conversation->title}");
        } else {
            $conversation = AIConversation::create([
                'session_id' => 'cli-test-' . uniqid(),
                'title' => 'CLI Test Conversation',
                'status' => 'active',
                'last_activity_at' => now(),
            ]);
            $this->info("Created new conversation with ID: {$conversation->id}");
        }

        // Interactive mode
        if ($this->option('interactive')) {
            $this->runInteractiveMode($conversation);
            return Command::SUCCESS;
        }

        // Single message test
        $message = $this->ask('Enter your test message');
        $this->testMessage($message, $conversation);

        return Command::SUCCESS;
    }

    private function runInteractiveMode(AIConversation $conversation): void
    {
        $this->info('Interactive mode enabled. Type "exit" to quit.');

        while (true) {
            $message = $this->ask('You');

            if (strtolower($message) === 'exit') {
                break;
            }

            $this->testMessage($message, $conversation);
        }
    }

    private function testMessage(string $message, AIConversation $conversation): void
    {
        $this->info('Processing message...');

        $response = $this->aiAssistantService->processMessage($message, $conversation);

        $this->newLine();

        if ($response['success']) {
            $this->info('AI Assistant:');
            $this->line($response['content']);

            if (isset($response['metadata']) && isset($response['metadata']['sources'])) {
                $this->newLine();
                $this->info('Sources:');
                foreach ($response['metadata']['sources'] as $source) {
                    $this->line(" - {$source['title']}");
                }
            }
        } else {
            $this->error('AI Assistant Error:');
            $this->line($response['error'] ?? 'Unknown error occurred');
        }
    }
}
