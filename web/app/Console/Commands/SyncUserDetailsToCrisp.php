<?php

namespace App\Console\Commands;

use App\Jobs\UpdateDetailsToCrispJob;
use App\Models\Session;
use Illuminate\Console\Command;

class SyncUserDetailsToCrisp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:store-details-to-crisp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command syncs all the user details to crisp on daily basis';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $batchSize = 100;

        Session::whereNotNull('access_token')
            ->where('enable_sync', true)
            ->chunk($batchSize, function ($sessions) {
                foreach ($sessions as $session) {
                    UpdateDetailsToCrispJob::dispatch($session->id)->onQueue('sync_crisp_data');
                }
            });
    }
}
