<?php

namespace App\Console\Commands;

use App\Jobs\User\FetchUserPreferences;
use App\Models\Ebay\EbayUser;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Bus;

class SyncEbayUsersPreferences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-users-preferences';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetches user preferences from eBay';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $preferenceJobs = array();

        $users = EbayUser::with('userSetting')
            ->where('refresh_token_expired', 0)
            ->whereHas('session', function (Builder $query) {
                return $query->where('enable_sync', true);
            })
            ->whereHas('userSetting')
            ->get();

        foreach ($users as $ebayUser) {
            $preferenceJobs[] = new  FetchUserPreferences($ebayUser);
        }

        if ($preferenceJobs) {
            Bus::chain($preferenceJobs)
                ->onQueue('user_details')
                ->dispatch();
        }
    }
}
