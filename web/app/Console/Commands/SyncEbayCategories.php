<?php

namespace App\Console\Commands;

use App\Http\Controllers\Ebay\EbayCategoriesController;
use Illuminate\Console\Command;

class SyncEbayCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-categories {marketplace_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return true
     */
    public function handle(string $marketplace_id = '')
    {
        (new EbayCategoriesController())->fetchCategories($this->argument('marketplace_id'));
        echo('Successfully set corn to import ebay categories');
        return true;
    }
}
