<?php

namespace App\Console\Commands;

use App\Jobs\Notification\SendOrderSyncFailedEmailJob;
use App\Models\Ebay\EbayUserSetting;
use App\Models\Ebay\Order;
use App\Module\Shopify\Enums\ShopifyOrderSyncStatusEnum;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use JsonException;
use Psr\Http\Client\ClientExceptionInterface;
use Shopify\Exception\MissingArgumentException;
use Shopify\Exception\UninitializedContextException;

class SendOrderSyncFailedMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:order-sync-failed-mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends an email to the user when the order sync fails';


    /**
     * Execute the console command.
     *
     * @return void
     * @throws ClientExceptionInterface
     * @throws JsonException
     * @throws MissingArgumentException
     * @throws UninitializedContextException
     */
    public function handle(): void
    {
        Log::channel('daily')->info('Sending order sync failed email at ' . now());
        $time = time() - (config('ebay.order_sync_failed_mail_interval') * 60);
        $orders = Order::select('orders.id', 'orders.session_id', 'orders.ebay_user_id')
            ->join('ebay_user_settings as eus', 'orders.ebay_user_id', '=', 'eus.ebay_user_id')
            ->where('orders.order_sync_status', ShopifyOrderSyncStatusEnum::FAILED->value)
            ->where('orders.failed_at', '>', $time)
            ->where('eus.order_fail_notification', true) // Filter out users with notifications disabled
            ->where('eus.order_sync', true) // Filter out users with order syncing disabled
            ->get();
        foreach($orders->groupBy('session_id') as $sessionId => $orders) {
            $orderIds = $orders->pluck('id')->toArray();
            Log::info('Sending order sync failed email for session. ', [
                'sessionId' => $sessionId,
                'orderIds' => $orderIds
            ]);
            SendOrderSyncFailedEmailJob::dispatch(
                    sessionId: $sessionId,
                    orderIds: $orderIds
                )
                ->onQueue('email');
        }
    }
}
