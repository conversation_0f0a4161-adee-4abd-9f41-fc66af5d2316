<?php

namespace App\Console\Commands;

use App\Models\Session;
use App\Module\Shopify\Services\Collection\SyncShopifyCollectionService;
use Illuminate\Console\Command;

class SyncShopifyCollection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:shopify-collection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(SyncShopifyCollectionService $syncShopifyCollectionService)
    {
        $shops = Session::where('is_shopify_products_importing', 0)
            ->whereNotNull('access_token')
            ->where('enable_sync', true)
            ->get();
        foreach ($shops as $shop) {
            $syncShopifyCollectionService->setJob($shop);
            logger("setting Collection Sync job for $shop->shop");
        }
    }
}
