<?php

namespace App\Console\Commands;

use App\Jobs\FetchAndStoreEbayCategoryItemConditionsMetadataJob;
use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Services\User\EbaySiteMappingService;
use App\Module\Ebay\Services\User\UserMarketplaceIdService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class SyncEbayCategoryItemConditionsMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-category-item-conditions-metadata';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    public function handle(EbaySiteMappingService $ebaySiteMappingService): void
    {
        if (config('app.env') === 'production') {
            $ebayUser = EbayUser::where('ebay_user_name', 'testuser_exportfeed')
                ->orWhere('ebay_user_name', 'subasghimir_3')
                ->first();
        } else {
            $ebayUser = EbayUser::first();
        }

        if (!$ebayUser) {
            echo "No eBay User found so exiting\n";
            return;
        }

        $jobs = [];
        $ebaySites = $ebaySiteMappingService->getSites();
        foreach ($ebaySites as $site) {
            $jobs [] = new FetchAndStoreEbayCategoryItemConditionsMetadataJob(
                $ebaySiteMappingService->getSiteMap($site),
                $ebayUser
            );
        }

        if (!$jobs) {
            echo "No jobs set exiting\n";
            return;
        }
        Bus::chain($jobs)
            ->onQueue('category_details')
            ->dispatch();

        echo "Dispatched " . count($jobs) . " jobs to fetch category item conditions\n";
    }
}
