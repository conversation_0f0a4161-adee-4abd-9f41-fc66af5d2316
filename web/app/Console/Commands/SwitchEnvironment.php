<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use JsonException;

class SwitchEnvironment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'env:switch {environment}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Switch the application environment';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws JsonException
     */
    public function handle(): void
    {
        $environment = $this->argument('environment');

        // Path to the environment-specific .env file
        $envFilePath = base_path("ebayKeyset.json");

        if (!File::exists($envFilePath)) {
            $this->error("The .env file for environment '{$environment}' does not exist: {$envFilePath}");
            return;
        }


        // Parse the configuration file
        try {
            $ebayConfiguration = json_decode(File::get($envFilePath), true, 512, JSON_THROW_ON_ERROR)[$environment];
        } catch (JsonException $e) {
            $this->error("Failed to parse JSON configuration: {$e->getMessage()}");
            return;
        }

        // Ensure required keys exist in the configuration
        $requiredKeys = [
            'EBAY_RU_VALUE', 'EBAY_CLIENT_ID', 'EBAY_CLIENT_SECRET', 'EBAY_DEV_ID', 'EBAY_IS_SANDBOX'
        ];

        foreach ($requiredKeys as $key) {
            if (!array_key_exists($key, $ebayConfiguration)) {
                $this->error("Missing required key '{$key}' in configuration.");
                return;
            }
        }

        // Update the .env file
        $envFile = app()->environmentFilePath();

        if (!File::exists($envFile)) {
            $this->error("The .env file does not exist at path: {$envFile}");
            return;
        }

        try {
            $envContent = File::get($envFile);

            foreach ($ebayConfiguration as $key => $value) {
                $pattern = "/^{$key}=.*$/m"; // Match the existing key in the .env file
                $replacement = "{$key}={$value}";

                if (preg_match($pattern, $envContent)) {
                    // Replace existing value
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    // Add the key if it doesn't exist
                    $envContent .= PHP_EOL . $replacement;
                }
            }

            // Write the updated .env content back to the file
            File::put($envFile, $envContent);

            // Clear the configuration cache
            $this->call('config:clear');

            $this->info("Environment switched to '{$environment}' and .env updated successfully.");
        } catch (\Exception $e) {
            $this->error("Failed to update the .env file: {$e->getMessage()}");
        }
    }
}
