<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Lib\Handlers\AWS\Sqs\SqsQueueConnector;
use App\Module\Shopify\Handlers\ShopifyWebhookMessageHandler;
use App\Services\Common\WorkerStopManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PollSqsStandardQueueMessages extends Command
{
    protected $signature = 'sqs-standard:poll {queueName}';
    protected $description = 'Polls messages from a SQS standard queue';

    private WorkerStopManager $workerStopManager;

    public function __construct(
        private readonly SqsQueueConnector $sqsQueueConnector,
        private readonly ShopifyWebhookMessageHandler $shopifyWebhookMessageHandler
    ) {
        parent::__construct();
        $this->workerStopManager = new WorkerStopManager('sqs:standard:poll:restart');
    }

    public function handle(): int
    {
        $this->info('Started command for polling SQS messages...');
        Log::channel('daily')->info('Standard Sqs Poll Command Started');

        $queueName = $this->argument('queueName');
        $sqsConfig = $this->getSqsConfig($queueName);

        $lastRestartTime = $this->workerStopManager->getTimestampOfLastQueueRestart();

        $queue = $this->sqsQueueConnector->connect($sqsConfig);
        $sqsClient = $queue->getSqs();
        $queueUrl = $queue->getQueue($queueName);

        $this->info('Connected to SQS for polling messages');
        Log::channel('daily')->info("Connected to Sqs for polling queue : $queueName ");

        while (true) {
            $this->shopifyWebhookMessageHandler->handle($sqsClient, $queueUrl);

            $status = $this->workerStopManager->stopIfNecessary($lastRestartTime);

            if ($status !== null) {
                return $status;
            }

            usleep(500000); // Sleep for 500 milliseconds before polling again
        }
    }

    private function getSqsConfig(string $queueName): array
    {
        $sqsConfig = config('queue.connections.sqs');
        $sqsConfig['queue'] = $queueName;

        return $sqsConfig;
    }
}
