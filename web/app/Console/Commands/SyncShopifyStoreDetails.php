<?php

namespace App\Console\Commands;

use App\Jobs\FetchShopifyDetails;
use App\Models\Session;
use Illuminate\Console\Command;

class SyncShopifyStoreDetails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:store-details';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $shops = Session::where('is_shopify_products_importing', 0)->get();
        foreach ($shops as $shop) {
            FetchShopifyDetails::dispatch($shop->session_id);
        }
    }
}
