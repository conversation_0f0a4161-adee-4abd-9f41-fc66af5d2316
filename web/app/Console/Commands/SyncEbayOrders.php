<?php

namespace App\Console\Commands;

use App\Jobs\Ebay\FetchOrder;
use App\Models\Ebay\EbayUser;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class SyncEbayOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $users = EbayUser::with('userSetting')
                ->where('refresh_token_expired', 0)
                ->whereHas('session', function (Builder $query) {
                    return $query->where('enable_sync', true);
                })
                ->get();
            foreach ($users as $ebayUser) {
                FetchOrder::dispatch($ebayUser)->onQueue('order_sync');
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error("Error while fetching EbayOrders", [
                'Message'=>$e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
}
