<?php

namespace App\Console\Commands;

use App\Models\Session;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use JsonException;
use Psr\Http\Client\ClientExceptionInterface;
use Shopify\Clients\Rest;
use Shopify\Exception\MissingArgumentException;
use Shopify\Exception\UninitializedContextException;

class UnsubscribeOrderFulfilledWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'unsubscribe:order-fulfillment-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'unsubscribe:order-fulfillment-webhook';


    /**
     * Execute the console command.
     *
     * @return void
     * @throws ClientExceptionInterface
     * @throws JsonException
     * @throws MissingArgumentException
     * @throws UninitializedContextException
     */
    public function handle(): void
    {
        $this->info('Started unsubscribing webhook ...');

        Session::query()->select('id', 'shop', 'access_token')
            ->where('enable_sync', 1)
            ->whereNotNull('access_token')
            ->lazyByIdDesc(100, 'id')
            ->each(function ($shop) {
                $client = new Rest($shop->shop, $shop->access_token);
                $result = $client->get('webhooks');

                $result = $result->getDecodedBody();

                if (Arr::has($result, 'errors')) {
                    Log::channel('daily')->info('Could not unsubscribe order fulfilled webhook', [
                        'shopName' => $shop->shop,
                        'reason' => $result
                    ]);
                    return;
                }


                $webhooks = Arr::get($result, 'webhooks', []);

                $fulfilledWebhookId = null;

                foreach ($webhooks as $webhook) {
                    if ($webhook['topic'] === 'orders/fulfilled') {
                        $fulfilledWebhookId = $webhook['id'];
                        break;
                    }
                }

                if ($fulfilledWebhookId !== null) {
                    $client->delete("webhooks/$fulfilledWebhookId");
                    Log::channel('daily')->info('Unsubscribed order fulfilled webhook', [
                    'webhookId' => $fulfilledWebhookId,
                    'shopName' => $shop->shop
                    ]);
                }
            });

        $this->info("Finished unsubscribing webhook .");
    }
}
