<?php

namespace App\Console\Commands;

use App\Services\AI\Knowledge\KnowledgeIngestionService;
use App\Services\AI\VectorStore\PineconeService;
use Illuminate\Console\Command;

class IngestKnowledgeBase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:ingest-knowledge 
                            {--type=all : Type of knowledge to ingest (all, app, platform, troubleshooting)}
                            {--force : Force re-ingestion of existing documents}
                            {--setup : Setup Pinecone index}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ingest knowledge base documents for AI assistant';

    private KnowledgeIngestionService $ingestionService;
    private PineconeService $pineconeService;

    public function __construct(
        KnowledgeIngestionService $ingestionService,
        PineconeService $pineconeService
    ) {
        parent::__construct();
        $this->ingestionService = $ingestionService;
        $this->pineconeService = $pineconeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Starting AI Knowledge Base Ingestion');

        // Setup Pinecone index if requested
        if ($this->option('setup')) {
            $this->setupPineconeIndex();
        }

        $type = $this->option('type');
        $force = $this->option('force');

        try {
            switch ($type) {
                case 'app':
                    $this->ingestAppDomainKnowledge($force);
                    break;
                case 'platform':
                    $this->ingestPlatformKnowledge($force);
                    break;
                case 'troubleshooting':
                    $this->ingestTroubleshootingKnowledge($force);
                    break;
                case 'all':
                default:
                    $this->ingestAllKnowledge($force);
                    break;
            }

            $this->info('✅ Knowledge base ingestion completed successfully!');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Knowledge base ingestion failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Setup Pinecone index.
     */
    private function setupPineconeIndex(): void
    {
        $this->info('🔧 Setting up Pinecone index...');

        if ($this->pineconeService->createIndexIfNotExists()) {
            $this->info('✅ Pinecone index setup completed');
        } else {
            $this->error('❌ Failed to setup Pinecone index');
            exit(Command::FAILURE);
        }
    }

    /**
     * Ingest all knowledge types.
     */
    private function ingestAllKnowledge(bool $force): void
    {
        $this->info('📚 Ingesting all knowledge types...');

        $this->ingestAppDomainKnowledge($force);
        $this->ingestPlatformKnowledge($force);
        $this->ingestTroubleshootingKnowledge($force);
    }

    /**
     * Ingest app domain knowledge.
     */
    private function ingestAppDomainKnowledge(bool $force): void
    {
        $this->info('🏢 Ingesting app domain knowledge...');

        $progressBar = $this->output->createProgressBar();
        $progressBar->start();

        $documents = $this->ingestionService->ingestAppDomainKnowledge();

        $progressBar->advance();
        $progressBar->finish();
        $this->newLine();

        $this->info("✅ Ingested " . count($documents) . " app domain documents");

        foreach ($documents as $document) {
            $this->line("   - {$document->title}");
        }
    }

    /**
     * Ingest platform knowledge.
     */
    private function ingestPlatformKnowledge(bool $force): void
    {
        $this->info('🛒 Ingesting platform knowledge...');

        $progressBar = $this->output->createProgressBar();
        $progressBar->start();

        $documents = $this->ingestionService->ingestPlatformKnowledge();

        $progressBar->advance();
        $progressBar->finish();
        $this->newLine();

        $this->info("✅ Ingested " . count($documents) . " platform documents");

        foreach ($documents as $document) {
            $this->line("   - {$document->title}");
        }
    }

    /**
     * Ingest troubleshooting knowledge.
     */
    private function ingestTroubleshootingKnowledge(bool $force): void
    {
        $this->info('🔧 Ingesting troubleshooting knowledge...');

        // This would be implemented when we have specific troubleshooting documents
        $this->line('   - Troubleshooting knowledge included in other categories');
    }

    /**
     * Display ingestion statistics.
     */
    private function displayStatistics(): void
    {
        $this->info('📊 Knowledge Base Statistics:');

        // Get Pinecone index stats
        $stats = $this->pineconeService->getIndexStats();

        if ($stats['success']) {
            $vectorCount = $stats['stats']['totalVectorCount'] ?? 0;
            $this->line("   - Total vectors in Pinecone: {$vectorCount}");
        }

        // Get database stats
        $documentCount = \App\Models\AI\AIKnowledgeDocument::count();
        $chunkCount = \App\Models\AI\AIKnowledgeChunk::count();

        $this->line("   - Total documents: {$documentCount}");
        $this->line("   - Total chunks: {$chunkCount}");

        // Category breakdown
        $categories = \App\Models\AI\AIKnowledgeDocument::selectRaw('category, count(*) as count')
            ->groupBy('category')
            ->get();

        $this->line('   - Documents by category:');
        foreach ($categories as $category) {
            $this->line("     • {$category->category}: {$category->count}");
        }
    }
}
