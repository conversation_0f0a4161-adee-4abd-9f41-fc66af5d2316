<?php

namespace App\Console\Commands;

use App\Jobs\Policy\FetchEbayPaymentPolicy;
use App\Jobs\Policy\FetchEbayReturnPolicy;
use App\Jobs\Policy\FetchEbayShippingPolicy;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\Policy\EbayPaymentPolicy;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class SyncEbayPolicies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ebay-policies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync eBay polies to our system';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = EbayUser::with('userSetting')
            ->where('refresh_token_expired', 0)
            ->whereHas('session', function (Builder $query) {
                return $query->where('enable_sync', true);
            })->get();
        foreach ($users as $ebayUser) {
            FetchEbayPaymentPolicy::dispatch($ebayUser)->onQueue('user_details');
            FetchEbayShippingPolicy::dispatch($ebayUser)->onQueue('user_details');
            FetchEbayReturnPolicy::dispatch($ebayUser)->onQueue('user_details');
        }
    }
}
