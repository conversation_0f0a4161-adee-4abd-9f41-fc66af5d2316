<?php

namespace App\Console\Commands;

use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Services\OAuth\EbayOAuthService;
use Carbon\Carbon;
use DTS\eBaySDK\OAuth\Services\OAuthService;
use DTS\eBaySDK\OAuth\Types\RefreshUserTokenRestRequest;
use Illuminate\Console\Command;

class ToggleTokenExpiryForEbayUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'toggle:expiry {ebayUserId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Toggle token expiry for ebay user. Use only for expiry testing';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        private readonly EbayOAuthService $ebayOAuthService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        /** @var EbayUser $ebayUser */
        $ebayUser = EbayUser::query()->where('id', $this->argument('ebayUserId'))->first();
        if (!$ebayUser) {
            $this->error("No ebay user found");
            return;
        }

        $toggleTo = $ebayUser->refresh_token_expired ? 'Off' : 'On';

        if ($this->confirm("Do you wish to set token expiry : $toggleTo ?")) {
            $accessTokenExpiresAt =  $ebayUser->access_token_expires_at;
            $refreshTokenExpiresAt =  $ebayUser->access_token_expires_at;

            if ($ebayUser->refresh_token_expired) {
                $oAuthService =  new OAuthService($this->ebayOAuthService->eBayCredentials());
                $refreshTokenArgs = $this->ebayOAuthService->getRefreshTokenArgs($ebayUser->refresh_token);
                $response = $oAuthService->refreshUserToken(new RefreshUserTokenRestRequest($refreshTokenArgs));


                $accessTokenExpiresIn = date('Y-m-d H:i:s', (time() + $response->expires_in));

                $ebayUser->update([
                    'access_token' => $response->access_token,
                    'access_token_expires_at' => $accessTokenExpiresIn,
                    'refresh_token_expires_at' => Carbon::parse($accessTokenExpiresIn)->addYears(2),
                    'refresh_token_expired' => 0
                ]);
            } else {
                $ebayUser->update([
                    'access_token_expires_at' => Carbon::parse($accessTokenExpiresAt)->subDecade(),
                    'refresh_token_expires_at' => Carbon::parse($refreshTokenExpiresAt)->subDecade(),
                    'refresh_token_expired' => 1
                ]);
            }
            $expiryStatus = $ebayUser->refresh_token_expired ? 'On' : 'Off';
            $this->info("Refresh token expiry : $expiryStatus");
        }
    }
}
