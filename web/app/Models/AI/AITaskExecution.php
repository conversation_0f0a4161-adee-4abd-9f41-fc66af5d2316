<?php

namespace App\Models\AI;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AITaskExecution extends Model
{
    use HasFactory;

    protected $table = 'ai_task_executions';

    protected $fillable = [
        'conversation_id',
        'task_type',
        'parameters',
        'status',
        'result',
        'error_message',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'parameters' => 'array',
        'result' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the conversation that owns the task execution.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(AIConversation::class, 'conversation_id');
    }

    /**
     * Mark task as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'executing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark task as completed.
     */
    public function markAsCompleted(array $result = []): void
    {
        $this->update([
            'status' => 'completed',
            'result' => $result,
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark task as failed.
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Get task parameter value.
     */
    public function getParameter(string $key, $default = null)
    {
        return data_get($this->parameters, $key, $default);
    }

    /**
     * Get task result value.
     */
    public function getResult(string $key, $default = null)
    {
        return data_get($this->result, $key, $default);
    }

    /**
     * Get execution duration in seconds.
     */
    public function getExecutionDuration(): ?int
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * Check if task is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if task is executing.
     */
    public function isExecuting(): bool
    {
        return $this->status === 'executing';
    }

    /**
     * Check if task is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if task failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Scope for tasks by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for pending tasks.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for executing tasks.
     */
    public function scopeExecuting($query)
    {
        return $query->where('status', 'executing');
    }

    /**
     * Scope for completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed tasks.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
}
