<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // AI Conversations Table
        Schema::create('ai_conversations', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index();
            $table->unsignedBigInteger('ebay_user_id')->nullable()->index();
            $table->string('title')->nullable();
            $table->json('context')->nullable();
            $table->enum('status', ['active', 'archived'])->default('active');
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();

            $table->foreign('ebay_user_id')->references('id')->on('ebay_users')->onDelete('cascade');
            $table->index(['session_id', 'status']);
        });

        // AI Messages Table
        Schema::create('ai_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('conversation_id')->index();
            $table->enum('role', ['user', 'assistant', 'system']);
            $table->text('content');
            $table->json('metadata')->nullable();
            $table->integer('tokens_used')->default(0);
            $table->timestamp('created_at');

            $table->foreign('conversation_id')->references('id')->on('ai_conversations')->onDelete('cascade');
            $table->index(['conversation_id', 'created_at']);
        });

        // AI Knowledge Documents Table
        Schema::create('ai_knowledge_documents', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->enum('category', [
                'app_domain',
                'shopify_platform', 
                'ebay_platform',
                'troubleshooting'
            ])->index();
            $table->string('subcategory', 100)->nullable()->index();
            $table->json('tags')->nullable();
            $table->string('pinecone_id')->unique()->nullable();
            $table->json('embedding_metadata')->nullable();
            $table->string('source_file')->nullable();
            $table->string('content_hash', 64)->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'subcategory']);
            $table->index(['is_active', 'category']);
        });

        // AI Task Executions Table
        Schema::create('ai_task_executions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('conversation_id')->index();
            $table->string('task_type', 100)->index();
            $table->json('parameters');
            $table->enum('status', ['pending', 'executing', 'completed', 'failed'])->default('pending');
            $table->json('result')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->foreign('conversation_id')->references('id')->on('ai_conversations')->onDelete('cascade');
            $table->index(['status', 'task_type']);
            $table->index(['conversation_id', 'status']);
        });

        // AI Knowledge Chunks Table (for document chunking)
        Schema::create('ai_knowledge_chunks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_id')->index();
            $table->text('content');
            $table->integer('chunk_index');
            $table->string('pinecone_vector_id')->unique();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('document_id')->references('id')->on('ai_knowledge_documents')->onDelete('cascade');
            $table->index(['document_id', 'chunk_index']);
        });

        // AI Usage Statistics Table
        Schema::create('ai_usage_statistics', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index();
            $table->unsignedBigInteger('ebay_user_id')->nullable()->index();
            $table->date('date')->index();
            $table->integer('messages_sent')->default(0);
            $table->integer('tokens_used')->default(0);
            $table->integer('tasks_executed')->default(0);
            $table->integer('documents_retrieved')->default(0);
            $table->json('feature_usage')->nullable();
            $table->timestamps();

            $table->foreign('ebay_user_id')->references('id')->on('ebay_users')->onDelete('cascade');
            $table->unique(['session_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ai_usage_statistics');
        Schema::dropIfExists('ai_knowledge_chunks');
        Schema::dropIfExists('ai_task_executions');
        Schema::dropIfExists('ai_knowledge_documents');
        Schema::dropIfExists('ai_messages');
        Schema::dropIfExists('ai_conversations');
    }
};
