{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "UNLICENSED", "require": {"php": "^8.1", "ext-json": "*", "ext-pcntl": "*", "ext-xml": "*", "ext-zip": "*", "aws/aws-sdk-php": "^3.306", "crispchat/php-crisp-api": "^1.7", "doctrine/dbal": "^3.1", "dpl/shopifysync": "^2.1.2", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^3.0", "guzzlehttp/guzzle": "^7.1", "guzzlehttp/psr7": "^2.6", "helpscout/api": "^3.0", "intervention/image": "^2.7", "laravel/framework": "^8.12", "laravel/horizon": "^5.21", "laravel/slack-notification-channel": "^2.5", "laravel/tinker": "^2.5", "mongodb/laravel-mongodb": "^3", "php-http/guzzle7-adapter": "^1.0", "predis/predis": "^2.2", "pusher/pusher-php-server": "^7.2", "shopify/shopify-api": "^5.8", "shrestharikesh/ebay-sdk-php": "^1.0", "openai-php/laravel": "^0.8.1"}, "require-dev": {"ekino/phpstan-banned-code": "^1.0", "ergebnis/phpstan-rules": "^1.0", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "larastan/larastan": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpstan/phpstan-deprecation-rules": "1.0", "phpstan/phpstan-strict-rules": "1.4", "phpunit/phpunit": "^9.3.3", "slam/phpstan-extensions": "6.0.0", "squizlabs/php_codesniffer": "^3.6", "thecodingmachine/phpstan-strict-rules": "^1.0", "wterberg/phpstan-laravel": "1.0.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "serve": ["Composer\\Config::disableProcessTimeout", "php artisan serve"], "react": "npm run watch", "lint": "./vendor/bin/phpcs --standard=PSR12 app routes", "build": "composer build-frontend-links", "build-frontend-links": "ln -sf ../frontend/dist/assets public/assets && ln -sf ../frontend/dist/index.html public/index.html"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": false}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "*****************:dpl-workspace/shopify-sync-graphql.git"}]}