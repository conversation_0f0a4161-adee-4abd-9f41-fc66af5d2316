# eBay Category Hierarchy Guide

## Understanding eBay Categories

### Category Structure
eBay uses a hierarchical category system with multiple levels:
- **Level 1**: Top-level categories (e.g., Electronics, Fashion)
- **Level 2**: Subcategories (e.g., Cell Phones & Accessories)
- **Level 3**: Specific categories (e.g., Cell Phone Cases)
- **Level 4+**: Highly specific categories (e.g., iPhone Cases)

### Category IDs
Each category has a unique numeric ID that remains constant across eBay sites. These IDs are used in API calls and listing operations.

## Major Category Groups

### Electronics & Technology
- **Computers/Tablets & Networking** (58058)
  - Laptops & Netbooks
  - Desktop & All-In-One Computers
  - Tablets & eBook Readers
  - Computer Components & Parts

- **Cell Phones & Accessories** (15032)
  - Cell Phones & Smartphones
  - Cases, Covers & Skins
  - Chargers & Cradles
  - Bluetooth Headsets

- **Consumer Electronics** (293)
  - TV, Video & Home Audio
  - Cameras & Photo
  - Video Games & Consoles
  - Portable Audio & Headphones

### Fashion & Accessories
- **Clothing, Shoes & Accessories** (11450)
  - Women's Clothing
  - Men's Clothing
  - Kids' Clothing
  - Shoes
  - Jewelry & Watches

- **Handbags & Purses** (169291)
  - Women's Handbags
  - Men's Bags
  - Kids' Bags
  - Luggage

### Home & Garden
- **Home & Garden** (11700)
  - Home Décor
  - Kitchen, Dining & Bar
  - Bedding
  - Bath
  - Yard, Garden & Outdoor Living

- **Furniture** (3197)
  - Living Room Furniture
  - Bedroom Furniture
  - Kitchen & Dining Room Furniture
  - Office Furniture

### Automotive
- **eBay Motors** (6000)
  - Cars & Trucks
  - Motorcycle Parts
  - Automotive Tools & Supplies
  - Boat Parts

## Category Selection Best Practices

### Research Before Listing
1. **Browse Similar Items**: See where competitors list similar products
2. **Use eBay's Category Finder**: Utilize eBay's built-in category suggestion tool
3. **Check Category Requirements**: Review specific requirements for your chosen category

### Specificity Matters
- Choose the most specific category that fits your item
- Avoid generic categories when specific ones are available
- More specific categories often have better visibility

### Category Restrictions
Some categories have special requirements:
- **Automotive**: May require compatibility information
- **Electronics**: Often need brand and model specifics
- **Health & Beauty**: May have FDA or safety requirements
- **Collectibles**: Often require authentication or grading

## Item Specifics by Category

### Electronics Categories
Common required specifics:
- Brand
- Model
- Condition
- Connectivity
- Storage Capacity (for devices)
- Screen Size (for displays)

### Fashion Categories
Common required specifics:
- Brand
- Size
- Color
- Material
- Condition
- Style
- Season

### Home & Garden Categories
Common required specifics:
- Brand
- Material
- Color
- Room
- Style
- Dimensions

## Category-Specific Policies

### Prohibited Items by Category
- **Electronics**: No counterfeit items, proper warranty disclosure required
- **Fashion**: No replica designer items, accurate size information required
- **Health**: No prescription medications, FDA compliance required
- **Automotive**: No recalled items, compatibility information required

### Special Requirements
- **Books**: ISBN required for most listings
- **Media**: UPC/EAN codes often required
- **Collectibles**: Authentication may be required for high-value items
- **Business & Industrial**: Professional certifications may be needed

## Multi-Category Listings

### When to Use Secondary Categories
- Items that fit multiple categories
- Increased visibility for competitive items
- Cross-category appeal products

### Best Practices
- Primary category should be most specific
- Secondary category can be broader for visibility
- Additional fees apply for secondary categories

## Category Changes and Updates

### eBay Category Updates
- eBay periodically updates category structure
- Some categories may be merged or split
- Deprecated categories are gradually phased out

### Staying Current
- Monitor eBay seller updates
- Use category validation APIs
- Regular category audits for large inventories

## API Integration

### Category API Endpoints
- **GetCategories**: Retrieve category hierarchy
- **GetCategoryFeatures**: Get category-specific features and requirements
- **GetCategorySpecifics**: Retrieve required item specifics

### Category Validation
- Validate category IDs before listing
- Check for category restrictions
- Verify item specifics requirements

## Tools and Resources

### eBay Category Tools
- Category Browser on eBay.com
- Seller Hub category insights
- eBay Developer Program documentation

### Third-Party Tools
- Category research tools
- Competitive analysis platforms
- Listing optimization services

## Common Mistakes to Avoid

### Category Selection Errors
- Choosing overly broad categories
- Ignoring category-specific requirements
- Not researching competitor placements

### Item Specifics Mistakes
- Leaving required fields empty
- Using inconsistent values
- Not following eBay's accepted values

### Policy Violations
- Listing prohibited items
- Ignoring category restrictions
- Not meeting authentication requirements
