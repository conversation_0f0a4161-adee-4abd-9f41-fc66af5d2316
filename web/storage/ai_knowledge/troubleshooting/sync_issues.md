# Sync Issues Troubleshooting Guide

## Common Sync Problems

### 1. Products Not Syncing to eBay

#### Symptoms
- Products remain in "pending" status
- No eBay listings created after sync attempt
- Error messages in sync logs

#### Possible Causes
- **Authentication Issues**: Expired eBay tokens
- **Category Problems**: Invalid or restricted categories
- **Missing Required Fields**: Incomplete product information
- **Policy Violations**: Products violate eBay policies

#### Troubleshooting Steps
1. **Check Authentication**
   - Verify eBay token status in settings
   - Re-authenticate if tokens are expired
   - Confirm API permissions are correct

2. **Validate Product Data**
   - Ensure all required fields are filled
   - Check category compatibility
   - Verify item specifics are complete

3. **Review Error Logs**
   - Check sync error messages
   - Look for specific API error codes
   - Cross-reference with eBay API documentation

### 2. Inventory Sync Failures

#### Symptoms
- Inventory levels don't match between platforms
- Overselling incidents
- Manual inventory corrections needed frequently

#### Possible Causes
- **API Rate Limits**: Too many requests in short time
- **Network Issues**: Connectivity problems
- **SKU Mismatches**: Products not properly linked
- **Timing Conflicts**: Simultaneous updates from both platforms

#### Troubleshooting Steps
1. **Check API Status**
   - Monitor API rate limit usage
   - Verify network connectivity
   - Test API endpoints manually

2. **Validate SKU Mappings**
   - Ensure SKUs match exactly between platforms
   - Check for duplicate SKUs
   - Verify variant mappings are correct

3. **Review Sync Timing**
   - Check sync frequency settings
   - Look for timing conflicts in logs
   - Adjust sync intervals if needed

### 3. Order Sync Problems

#### Symptoms
- eBay orders not appearing in Shopify
- Duplicate orders created
- Order status not updating correctly

#### Possible Causes
- **Webhook Failures**: eBay notifications not received
- **Order Processing Errors**: Invalid order data
- **Status Mapping Issues**: Incorrect status translations
- **Customer Data Problems**: Missing or invalid customer information

#### Troubleshooting Steps
1. **Verify Webhook Configuration**
   - Check eBay webhook settings
   - Test webhook endpoints
   - Review webhook delivery logs

2. **Validate Order Data**
   - Check for required customer information
   - Verify product mappings exist
   - Ensure shipping addresses are valid

3. **Review Status Mappings**
   - Confirm order status translations
   - Check payment status handling
   - Verify shipping status updates

## Error Code Reference

### eBay API Error Codes

#### Authentication Errors
- **21916**: Token has expired
- **21917**: Invalid token
- **21919**: Insufficient permissions

**Solution**: Re-authenticate eBay account and refresh tokens

#### Listing Errors
- **21916**: Category not allowed for account
- **240**: Duplicate listing detected
- **21919**: Missing required item specifics

**Solution**: Review category selection and complete all required fields

#### Inventory Errors
- **21916**: Invalid quantity value
- **240**: SKU already exists
- **21919**: Inventory location not found

**Solution**: Verify inventory data and location settings

### Shopify API Error Codes

#### Product Errors
- **422**: Validation failed
- **429**: Rate limit exceeded
- **404**: Product not found

**Solution**: Check product data and API rate limits

#### Inventory Errors
- **422**: Invalid inventory adjustment
- **404**: Inventory item not found
- **429**: Too many requests

**Solution**: Verify inventory operations and reduce request frequency

## Diagnostic Tools

### Sync Status Dashboard
- Real-time sync status monitoring
- Error count and success rate metrics
- Recent sync activity timeline

### Log Analysis Tools
- Detailed error log viewer
- Search and filter capabilities
- Export functionality for external analysis

### API Testing Tools
- Manual API endpoint testing
- Token validation checker
- Rate limit monitoring

## Prevention Strategies

### Regular Maintenance
1. **Token Refresh**: Set up automatic token renewal
2. **Data Validation**: Regular product data audits
3. **Performance Monitoring**: Track sync performance metrics

### Error Monitoring
1. **Alert Setup**: Configure notifications for sync failures
2. **Log Retention**: Maintain detailed logs for troubleshooting
3. **Escalation Procedures**: Define when to contact support

### Best Practices
1. **Gradual Rollouts**: Test changes with small product sets first
2. **Backup Procedures**: Maintain data backups before major changes
3. **Documentation**: Keep detailed records of configuration changes

## Advanced Troubleshooting

### Network Diagnostics
- Test connectivity to eBay and Shopify APIs
- Check for firewall or proxy issues
- Verify SSL certificate validity

### Database Analysis
- Check for data corruption
- Analyze sync queue status
- Review database performance metrics

### Performance Optimization
- Identify bottlenecks in sync process
- Optimize database queries
- Implement caching strategies

## When to Contact Support

### Escalation Criteria
- Persistent sync failures after following troubleshooting steps
- API errors not covered in documentation
- Data corruption or loss incidents
- Performance issues affecting business operations

### Information to Provide
1. **Error Details**: Specific error messages and codes
2. **Timeline**: When issues started and frequency
3. **Environment**: Account details and configuration
4. **Steps Taken**: Troubleshooting attempts already made

### Support Channels
- In-app support ticket system
- Email support with detailed logs
- Emergency contact for critical issues
- Community forums for general questions
