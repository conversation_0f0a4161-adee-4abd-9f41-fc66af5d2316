# AI Assistant Environment Configuration
# Copy this file to your main Laravel .env file and configure the values

# =============================================================================
# OpenAI Configuration
# =============================================================================
# Get your API key from https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORGANIZATION=your_openai_org_id_here

# Model Configuration
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=60

# =============================================================================
# Pinecone Configuration
# =============================================================================
# Get your API key from https://app.pinecone.io/
PINECONE_API_KEY=your_pinecone_api_key_here

# For serverless indexes (recommended)
PINECONE_HOST=https://shopify-ebay-dpl-8kz58od.svc.aped-4627-b74a.pinecone.io

# For pod-based indexes (legacy)
# PINECONE_ENVIRONMENT=your_pinecone_environment
# PINECONE_INDEX_NAME=shopify-ebay-dpl

# Index Configuration
PINECONE_INDEX_NAME=shopify-ebay-dpl
PINECONE_DIMENSION=3072
PINECONE_METRIC=cosine
PINECONE_TIMEOUT=30

# =============================================================================
# Knowledge Base Configuration
# =============================================================================
# Document Processing Settings
AI_CHUNK_SIZE=1000
AI_CHUNK_OVERLAP=200
AI_MAX_CHUNKS_PER_DOCUMENT=50
AI_SIMILARITY_THRESHOLD=0.7
AI_MAX_RETRIEVED_CHUNKS=10

# =============================================================================
# Conversation Configuration
# =============================================================================
# Conversation Management
AI_MAX_MESSAGES=50
AI_CONTEXT_WINDOW=10
AI_CONVERSATION_TIMEOUT=24
AI_AUTO_ARCHIVE_DAYS=30

# =============================================================================
# Task Execution Configuration
# =============================================================================
# AI Task Settings
AI_TASKS_ENABLED=true
AI_TASK_TIMEOUT=300
AI_MAX_CONCURRENT_TASKS=5

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
# API Rate Limits
AI_REQUESTS_PER_MINUTE=60
AI_REQUESTS_PER_HOUR=1000
AI_TOKENS_PER_DAY=100000

# =============================================================================
# Logging Configuration
# =============================================================================
# AI Assistant Logging
AI_LOGGING_ENABLED=true
AI_LOG_CHANNEL=ai_assistant
AI_LOG_LEVEL=info
AI_LOG_CONVERSATIONS=true
AI_LOG_EMBEDDINGS=false

# =============================================================================
# Development/Testing Configuration
# =============================================================================
# Set to true for development environments
AI_DEBUG_MODE=false
AI_MOCK_APIS=false
AI_SKIP_EMBEDDINGS=false

# =============================================================================
# Performance Configuration
# =============================================================================
# Performance Tuning
AI_CACHE_EMBEDDINGS=true
AI_CACHE_TTL=3600
AI_BATCH_SIZE=10
AI_QUEUE_DRIVER=redis

# =============================================================================
# Security Configuration
# =============================================================================
# Security Settings
AI_ENCRYPT_CONVERSATIONS=true
AI_ANONYMIZE_LOGS=true
AI_MAX_FILE_SIZE=10240
AI_ALLOWED_FILE_TYPES=txt,md,markdown

# =============================================================================
# Feature Flags
# =============================================================================
# Enable/Disable Features
AI_CHAT_ENABLED=true
AI_KNOWLEDGE_SEARCH_ENABLED=true
AI_TASK_AUTOMATION_ENABLED=true
AI_ANALYTICS_ENABLED=true
AI_ADMIN_INTERFACE_ENABLED=true

# =============================================================================
# Integration Configuration
# =============================================================================
# External Service Integration
AI_WEBHOOK_SECRET=your_webhook_secret_here
AI_API_VERSION=v1
AI_CORS_ENABLED=true

# =============================================================================
# Monitoring Configuration
# =============================================================================
# Monitoring and Alerting
AI_HEALTH_CHECK_ENABLED=true
AI_METRICS_ENABLED=true
AI_ALERT_EMAIL=<EMAIL>
AI_SLACK_WEBHOOK=your_slack_webhook_url

# =============================================================================
# Backup Configuration
# =============================================================================
# Data Backup Settings
AI_BACKUP_ENABLED=true
AI_BACKUP_FREQUENCY=daily
AI_BACKUP_RETENTION_DAYS=30
AI_BACKUP_STORAGE=s3
