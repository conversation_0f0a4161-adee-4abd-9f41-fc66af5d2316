<?php

declare(strict_types=1);

namespace App\Http\Controllers\Ebay\AI;

use App\Http\Controllers\Controller;
use App\Lib\Session;
use App\Models\ShopifyProduct;
use App\Module\Ebay\Services\AI\EbayTitleGeneratorService;
use App\Traits\ApiResponseTrait;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Throwable;

class TitleSuggestionController extends Controller
{
    use ApiResponseTrait;

    public function __construct(private readonly EbayTitleGeneratorService $ebayTitleGeneratorService)
    {
    }

    /**
     * @throws Throwable
     */
    public function getTitleSuggestion(Request $request, int $shopifyProductId): Response|Application|ResponseFactory
    {
        /** @var Session $shopifySession */
        $shopifySession = $request->get('shopifySession');
        $sessionId = $shopifySession->getSessionId();

        /** @var \App\Models\Session $session */
        $session  = $shopifySession->getSessionModel();

        if (!($session->subscribedPlan?->ai_suggestion_enabled)) {
            return $this->errorResponse(403, 'AI suggestion is not enabled for the subscribed plan');
        }

        /** @var ShopifyProduct $shopifyProduct */
        $shopifyProduct = ShopifyProduct::query()
            ->where('session_id', $sessionId)
            ->where('id', $shopifyProductId)
            ->first();

        if (!$shopifyProduct) {
            return $this->errorResponse(404, 'Product not found');
        }

        $profile = $shopifyProduct->profile;

        $characterLimit = 80
            - ($profile?->title_prefix ? strlen($profile->title_prefix) + 1 : 0)
            - ($profile?->title_suffix ? strlen($profile->title_suffix) + 1 : 0);

        $promptData = [
                'product_id' => $shopifyProduct->id,
                'title_output_count' => 1,
                'character_limit' => $characterLimit,
                'title' => $shopifyProduct->title,
                'description' => $shopifyProduct->descriptionHtml,
                'tags' => $shopifyProduct->tags
        ];

        $titles = $this->ebayTitleGeneratorService->generateEbayTitles($promptData);

        return $this->successResponse($titles);
    }
}
