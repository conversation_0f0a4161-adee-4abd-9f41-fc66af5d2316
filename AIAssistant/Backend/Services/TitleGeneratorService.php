<?php

declare(strict_types=1);

namespace App\Services\AI;

use App\Exceptions\OpenAIException;
use JsonException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

/**
 * Service for generating titles using OpenAI's GPT model
 */
class TitleGeneratorService
{
    private const MODEL = 'gpt-4o-mini';
    private const MAX_TOKENS = 100;
    private const TEMPERATURE = 0.7;
    private const CHAT_COMPLETION_API_URL = 'https://api.openai.com/v1/chat/completions';
    private const SOURCE = 'Title Generation';

    private readonly string $apiKey;

    public function __construct()
    {
        $this->apiKey = config('open_ai.api_key');
        if (empty($this->apiKey)) {
            throw new OpenAIException('OpenAI API key not configured', [
                'source' => self::SOURCE
            ]);
        }
    }

    /**
     * Generates titles using OpenAI's API based on provided prompts
     *
     * @param string $systemPrompt The system-level prompt to guide the AI
     * @param string $userPrompt The user's specific prompt for title generation
     * @return array Array containing the generated title
     * @throws OpenAIException When API request fails or response is invalid
     */
    public function generateTitles(string $systemPrompt, string $userPrompt): array
    {
        $response = $this->makeApiRequest($systemPrompt, $userPrompt);
        return $this->processResponse($response);
    }

    /**
     * Makes an API request to OpenAI's chat completion endpoint
     *
     * @param string $systemPrompt The system-level prompt
     * @param string $userPrompt The user's prompt
     * @return Response The HTTP response from OpenAI
     */
    private function makeApiRequest(string $systemPrompt, string $userPrompt): Response
    {
        return Http::withToken($this->apiKey)
            ->acceptJson()
            ->post(self::CHAT_COMPLETION_API_URL, [
                'model' => self::MODEL,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $userPrompt]
                ],
                'temperature' => self::TEMPERATURE,
                'max_tokens' => self::MAX_TOKENS,
                'response_format' => [
                    "type" => "json_schema",
                    "json_schema" => [
                        "name" => "title_generation",
                        "schema" => [
                            "type" => "object",
                            "properties" => [
                                "title" => [
                                    "type" => "string",
                                    "items" => ["type" => "string"]
                                ]
                            ],
                            "required" => ["title"],
                            "additionalProperties" => false
                        ],
                        "strict" => true
                    ]
                ]
            ]);
    }

    /**
     * Processes the API response and extracts the generated title
     *
     * @param Response $response The HTTP response from OpenAI
     * @return array Array containing the generated title
     * @throws OpenAIException When response processing fails
     */
    private function processResponse(Response $response): array
    {
        if (!$response->ok()) {
            throw new OpenAIException('OpenAI API request failed', [
                'source' => self::SOURCE,
                'status' => $response->status(),
                'body' => $response->body()
            ]);
        }

        $result = $response->json();

        Log::channel('open_ai_log')->info('OpenAI Token Usage', [
            'source' => self::SOURCE,
            'result' => $result
        ]);

        $responseContent = Arr::get($result, 'choices.0.message.content');
        if (!$responseContent) {
            throw new OpenAIException('Empty or missing response content from OpenAI', [
                'source' => self::SOURCE,
                'result' => $result
            ]);
        }

        try {
            $parsed = json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);
            $title = Arr::get($parsed, 'title');
            if (!$title) {
                throw new OpenAIException('Missing title in OpenAI response', [
                    'source' => self::SOURCE,
                    'message' => Arr::get($result, 'choices.0.message')
                ]);
            }
            return [$title];
        } catch (JsonException $e) {
            throw new OpenAIException('Failed to parse OpenAI response JSON', [
                'source' => self::SOURCE,
                'responseContent' => $responseContent,
                'finishReason' => Arr::get($result, 'choices.0.finish_reason'),
                'refusal' => Arr::get($result, 'choices.0.message.refusal'),
                'error' => $e->getMessage()
            ]);
        }
    }
}
