<?php

namespace App\Services\AI\VectorStore;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PineconeService
{
    private array $config;
    private string $indexName;
    private string $baseUrl;

    public function __construct()
    {
        $this->config = config('ai_assistant.pinecone');
        $this->indexName = $this->config['index_name'];

        // Support both serverless and pod-based Pinecone architectures
        if (!empty($this->config['host'])) {
            // Use direct host URL for serverless indexes
            $this->baseUrl = $this->config['host'];
        } else {
            // Fallback to old pod-based URL construction
            $this->baseUrl = "https://{$this->indexName}-{$this->config['environment']}.svc.{$this->config['environment']}.pinecone.io";
        }
    }

    /**
     * Create index if it doesn't exist.
     */
    public function createIndexIfNotExists(): bool
    {
        try {
            // For now, assume index exists or will be created manually
            // This is a simplified implementation
            Log::info("Pinecone index setup - assuming '{$this->indexName}' exists");
            return true;
        } catch (Exception $e) {
            Log::error('Failed to create Pinecone index', [
                'error' => $e->getMessage(),
                'index_name' => $this->indexName,
            ]);
            return false;
        }
    }

    /**
     * Upsert vectors to the index.
     */
    public function upsertVectors(array $vectors): bool
    {
        try {
            $response = Http::withHeaders([
                'Api-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/vectors/upsert", [
                'vectors' => $vectors,
            ]);

            if ($response->successful()) {
                Log::info('Successfully upserted vectors to Pinecone', [
                    'count' => count($vectors),
                    'index' => $this->indexName,
                ]);
                return true;
            } else {
                throw new Exception('Pinecone API error: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('Failed to upsert vectors to Pinecone', [
                'error' => $e->getMessage(),
                'vectors_count' => count($vectors),
                'index' => $this->indexName,
            ]);
            return false;
        }
    }

    /**
     * Search for similar vectors.
     */
    public function search(
        array $queryVector,
        int $topK = 10,
        array $filter = [],
        bool $includeMetadata = true
    ): array {
        try {
            $searchParams = [
                'vector' => $queryVector,
                'topK' => $topK,
                'includeMetadata' => $includeMetadata,
            ];

            if (!empty($filter)) {
                $searchParams['filter'] = $filter;
            }

            $response = Http::withHeaders([
                'Api-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/query", $searchParams);

            if ($response->successful()) {
                $data = $response->json();

                Log::debug('Pinecone search completed', [
                    'results_count' => count($data['matches'] ?? []),
                    'top_score' => $data['matches'][0]['score'] ?? 0,
                ]);

                return [
                    'success' => true,
                    'matches' => $data['matches'] ?? [],
                ];
            } else {
                throw new Exception('Pinecone API error: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('Pinecone search failed', [
                'error' => $e->getMessage(),
                'topK' => $topK,
                'filter' => $filter,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'matches' => [],
            ];
        }
    }

    /**
     * Delete vectors by IDs.
     */
    public function deleteVectors(array $ids): bool
    {
        try {
            $response = Http::withHeaders([
                'Api-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->delete("{$this->baseUrl}/vectors/delete", [
                'ids' => $ids,
            ]);

            if ($response->successful()) {
                Log::info('Successfully deleted vectors from Pinecone', [
                    'ids' => $ids,
                    'count' => count($ids),
                ]);
                return true;
            } else {
                throw new Exception('Pinecone API error: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('Failed to delete vectors from Pinecone', [
                'error' => $e->getMessage(),
                'ids' => $ids,
            ]);
            return false;
        }
    }

    /**
     * Delete all vectors matching filter.
     */
    public function deleteByFilter(array $filter): bool
    {
        try {
            $response = Http::withHeaders([
                'Api-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->delete("{$this->baseUrl}/vectors/delete", [
                'filter' => $filter,
            ]);

            if ($response->successful()) {
                Log::info('Successfully deleted vectors by filter from Pinecone', [
                    'filter' => $filter,
                ]);
                return true;
            } else {
                throw new Exception('Pinecone API error: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('Failed to delete vectors by filter from Pinecone', [
                'error' => $e->getMessage(),
                'filter' => $filter,
            ]);
            return false;
        }
    }

    /**
     * Get index statistics.
     */
    public function getIndexStats(): array
    {
        try {
            // Simplified implementation - return mock stats for now
            return [
                'success' => true,
                'stats' => [
                    'totalVectorCount' => 0,
                    'dimension' => $this->config['dimension'],
                ],
            ];
        } catch (Exception $e) {
            Log::error('Failed to get Pinecone index stats', [
                'error' => $e->getMessage(),
                'index' => $this->indexName,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate Pinecone configuration.
     */
    public function validateConfiguration(): bool
    {
        try {
            // Check if API key is set
            if (empty($this->config['api_key'])) {
                return false;
            }

            // Check if either host or environment is set
            return !empty($this->config['host']) || !empty($this->config['environment']);
        } catch (Exception $e) {
            Log::error('Pinecone configuration validation failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Format vector for upsert.
     */
    public function formatVector(string $id, array $values, array $metadata = []): array
    {
        return [
            'id' => $id,
            'values' => $values,
            'metadata' => $metadata,
        ];
    }

    /**
     * Generate unique vector ID.
     */
    public function generateVectorId(string $prefix = 'doc'): string
    {
        return $prefix . '_' . uniqid() . '_' . time();
    }
}
