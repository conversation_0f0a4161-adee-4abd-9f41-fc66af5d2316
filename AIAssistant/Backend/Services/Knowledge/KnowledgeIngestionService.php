<?php

namespace App\Services\AI\Knowledge;

use App\Models\AI\AIKnowledgeDocument;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class KnowledgeIngestionService
{
    private DocumentProcessor $documentProcessor;

    public function __construct(DocumentProcessor $documentProcessor)
    {
        $this->documentProcessor = $documentProcessor;
    }

    /**
     * Ingest knowledge from file.
     */
    public function ingestFromFile(
        string $filePath,
        string $category,
        ?string $subcategory = null,
        array $tags = []
    ): ?AIKnowledgeDocument {
        try {
            if (!Storage::exists($filePath)) {
                throw new Exception("File not found: {$filePath}");
            }

            $content = Storage::get($filePath);
            $title = $this->extractTitleFromPath($filePath);

            return $this->documentProcessor->processDocument(
                $title,
                $content,
                $category,
                $subcategory,
                $tags,
                $filePath
            );
        } catch (Exception $e) {
            Log::error('Failed to ingest knowledge from file', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Ingest knowledge from directory.
     */
    public function ingestFromDirectory(
        string $directoryPath,
        string $category,
        ?string $subcategory = null,
        array $tags = []
    ): array {
        $results = [];

        try {
            $files = Storage::files($directoryPath);

            Log::info('Directory ingestion started', [
                'directory' => $directoryPath,
                'files_found' => count($files),
                'files' => $files,
            ]);

            foreach ($files as $file) {
                Log::info('Processing file', [
                    'file' => $file,
                    'is_text_file' => $this->isTextFile($file),
                ]);

                if ($this->isTextFile($file)) {
                    $document = $this->ingestFromFile($file, $category, $subcategory, $tags);
                    if ($document) {
                        $results[] = $document;
                        Log::info('Successfully processed file', [
                            'file' => $file,
                            'document_id' => $document->id,
                        ]);
                    } else {
                        Log::warning('Failed to process file', ['file' => $file]);
                    }
                }
            }

            Log::info('Completed directory ingestion', [
                'directory' => $directoryPath,
                'files_processed' => count($results),
            ]);
        } catch (Exception $e) {
            Log::error('Failed to ingest knowledge from directory', [
                'directory' => $directoryPath,
                'error' => $e->getMessage(),
            ]);
        }

        return $results;
    }

    /**
     * Ingest app domain knowledge.
     */
    public function ingestAppDomainKnowledge(): array
    {
        $documents = [];

        // First, try to ingest from files
        $fileDocuments = $this->ingestFromDirectory(
            'ai_knowledge/app_domain',
            'app_domain',
            null,
            ['app_domain', 'integration', 'guide']
        );
        $documents = array_merge($documents, $fileDocuments);

        // Then add programmatically generated content
        $documents = array_merge($documents, $this->ingestProfileKnowledge());
        $documents = array_merge($documents, $this->ingestSyncKnowledge());
        $documents = array_merge($documents, $this->ingestIntegrationKnowledge());

        return $documents;
    }

    /**
     * Ingest profile management knowledge.
     */
    private function ingestProfileKnowledge(): array
    {
        $documents = [];

        // Profile Creation Guide
        $profileCreationContent = $this->generateProfileCreationGuide();
        Log::info('Processing Profile Creation Guide', ['content_length' => strlen($profileCreationContent)]);

        $doc = $this->documentProcessor->processDocument(
            'eBay Profile Creation Guide',
            $profileCreationContent,
            'app_domain',
            'profile_management',
            ['profile', 'creation', 'guide', 'ebay']
        );
        if ($doc) {
            $documents[] = $doc;
            Log::info('Successfully processed Profile Creation Guide', ['doc_id' => $doc->id]);
        } else {
            Log::error('Failed to process Profile Creation Guide');
        }

        // Category Selection Guide
        $categoryGuideContent = $this->generateCategorySelectionGuide();
        $doc = $this->documentProcessor->processDocument(
            'eBay Category Selection Guide',
            $categoryGuideContent,
            'app_domain',
            'profile_management',
            ['category', 'selection', 'ebay', 'mapping']
        );
        if ($doc) $documents[] = $doc;

        // Profile Attributes Guide
        $attributesGuideContent = $this->generateProfileAttributesGuide();
        $doc = $this->documentProcessor->processDocument(
            'Profile Attributes Configuration Guide',
            $attributesGuideContent,
            'app_domain',
            'profile_management',
            ['attributes', 'configuration', 'profile', 'item_specifics']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest sync operations knowledge.
     */
    private function ingestSyncKnowledge(): array
    {
        $documents = [];

        // Inventory Sync Guide
        $inventorySyncContent = $this->generateInventorySyncGuide();
        $doc = $this->documentProcessor->processDocument(
            'Inventory Synchronization Guide',
            $inventorySyncContent,
            'app_domain',
            'sync_operations',
            ['inventory', 'sync', 'shopify', 'ebay']
        );
        if ($doc) $documents[] = $doc;

        // Product Upload Process
        $productUploadContent = $this->generateProductUploadGuide();
        $doc = $this->documentProcessor->processDocument(
            'Product Upload Process Guide',
            $productUploadContent,
            'app_domain',
            'sync_operations',
            ['product', 'upload', 'process', 'sync']
        );
        if ($doc) $documents[] = $doc;

        // Order Sync Guide
        $orderSyncContent = $this->generateOrderSyncGuide();
        $doc = $this->documentProcessor->processDocument(
            'Order Synchronization Guide',
            $orderSyncContent,
            'app_domain',
            'sync_operations',
            ['order', 'sync', 'fulfillment', 'tracking']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest integration workflows knowledge.
     */
    private function ingestIntegrationKnowledge(): array
    {
        $documents = [];

        // Initial Setup Guide
        $setupContent = $this->generateInitialSetupGuide();
        $doc = $this->documentProcessor->processDocument(
            'Initial Setup and Configuration Guide',
            $setupContent,
            'app_domain',
            'integration_workflows',
            ['setup', 'configuration', 'onboarding', 'authentication']
        );
        if ($doc) $documents[] = $doc;

        // Troubleshooting Guide
        $troubleshootingContent = $this->generateTroubleshootingGuide();
        $doc = $this->documentProcessor->processDocument(
            'Common Issues and Troubleshooting Guide',
            $troubleshootingContent,
            'troubleshooting',
            'sync_issues',
            ['troubleshooting', 'errors', 'issues', 'solutions']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Ingest platform-specific knowledge.
     */
    public function ingestPlatformKnowledge(): array
    {
        $documents = [];

        // First, try to ingest from files
        $shopifyDocuments = $this->ingestFromDirectory(
            'ai_knowledge/shopify_platform',
            'shopify_platform',
            null,
            ['shopify', 'platform', 'guide']
        );
        $documents = array_merge($documents, $shopifyDocuments);

        $ebayDocuments = $this->ingestFromDirectory(
            'ai_knowledge/ebay_platform',
            'ebay_platform',
            null,
            ['ebay', 'platform', 'guide']
        );
        $documents = array_merge($documents, $ebayDocuments);

        // Also ingest troubleshooting files
        $troubleshootingDocuments = $this->ingestFromDirectory(
            'ai_knowledge/troubleshooting',
            'troubleshooting',
            null,
            ['troubleshooting', 'issues', 'guide']
        );
        $documents = array_merge($documents, $troubleshootingDocuments);

        // Then add programmatically generated content
        $shopifyContent = $this->generateShopifyPlatformGuide();
        $doc = $this->documentProcessor->processDocument(
            'Shopify Platform Integration Guide',
            $shopifyContent,
            'shopify_platform',
            'api_integration',
            ['shopify', 'platform', 'api', 'products', 'variants']
        );
        if ($doc) $documents[] = $doc;

        $ebayContent = $this->generateEbayPlatformGuide();
        $doc = $this->documentProcessor->processDocument(
            'eBay Platform Integration Guide',
            $ebayContent,
            'ebay_platform',
            'listing_requirements',
            ['ebay', 'platform', 'categories', 'policies', 'requirements']
        );
        if ($doc) $documents[] = $doc;

        return $documents;
    }

    /**
     * Extract title from file path.
     */
    private function extractTitleFromPath(string $filePath): string
    {
        $pathInfo = pathinfo($filePath);
        $filename = $pathInfo['filename']; // Gets filename without extension
        return ucwords(str_replace(['_', '-'], ' ', $filename));
    }

    /**
     * Check if file is a text file.
     */
    private function isTextFile(string $filePath): bool
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, ['txt', 'md', 'markdown']);
    }

    /**
     * Generate profile creation guide content.
     */
    private function generateProfileCreationGuide(): string
    {
        return <<<'EOD'
# eBay Profile Creation Guide

## What is an eBay Profile?

An eBay profile is a configuration template that defines how your Shopify products will be listed on eBay. It includes:
- Category mappings
- Item specifics
- Pricing rules
- Listing preferences
- Shipping settings

## Creating Your First Profile

### Step 1: Profile Setup
1. Navigate to Profiles section in your dashboard
2. Click "Create New Profile"
3. Enter a descriptive name (e.g., "Electronics - Smartphones")
4. Choose your target eBay site (eBay.com, eBay.co.uk, etc.)

### Step 2: Category Selection
- **Primary Category**: Select the most specific eBay category for your products
- **Secondary Category**: Optional for broader visibility
- Use the category browser to find the best match
- Consider where competitors list similar items

### Step 3: Item Specifics Configuration
Configure required and recommended item specifics:
- **Brand**: Usually required for most categories
- **Condition**: New, Used, Refurbished, etc.
- **MPN**: Manufacturer Part Number when available
- **Color, Size, Material**: Category-specific attributes

### Step 4: Pricing and Inventory
- Set base pricing rules
- Configure currency conversion
- Set up inventory sync preferences
- Define quantity limits

## Profile Templates

### Electronics Profile
- Pre-configured for tech products
- Includes brand, model, connectivity specifics
- Optimized for electronics categories

### Fashion Profile
- Set up for clothing and accessories
- Includes size, color, material specifics
- Gender and age group configurations

### Home & Garden Profile
- Configured for home products
- Room, style, and material specifics
- Dimension and color attributes

## Best Practices

1. **Use Specific Categories**: More specific categories often perform better
2. **Complete All Required Fields**: Missing specifics can hurt visibility
3. **Research Competitors**: See where similar items are successfully listed
4. **Test with Small Batches**: Start with a few products to validate settings
5. **Monitor Performance**: Track which profiles generate the most sales

## Common Issues

### Category Mismatch
- **Problem**: Products don't fit selected category
- **Solution**: Use eBay's category suggestion tool or browse similar listings

### Missing Required Specifics
- **Problem**: Listings rejected due to incomplete information
- **Solution**: Check eBay's category requirements and fill all mandatory fields

### Pricing Errors
- **Problem**: Incorrect pricing due to conversion or markup issues
- **Solution**: Verify currency settings and test calculations with sample products
EOD;
    }

    /**
     * Generate category selection guide content.
     */
    private function generateCategorySelectionGuide(): string
    {
        return <<<'EOD'
# eBay Category Selection Guide

## Understanding eBay Categories

eBay uses a hierarchical category system with multiple levels. Choosing the right category is crucial for:
- Product visibility in search results
- Compliance with eBay policies
- Proper item specifics requirements
- Fee calculations

## Category Research Process

### 1. Analyze Your Product
- Identify the primary function or use
- Note key characteristics (brand, type, material)
- Consider the target audience

### 2. Browse eBay Categories
- Start with broad categories and drill down
- Use eBay's category browser
- Look at the category tree structure

### 3. Research Competitors
- Search for similar products on eBay
- Note which categories successful sellers use
- Check completed listings for insights

### 4. Validate Category Requirements
- Review required item specifics
- Check for category restrictions
- Verify fee structures

## Category Selection Strategies

### Electronics Products
- Start with "Consumer Electronics" or "Computers/Tablets & Networking"
- Drill down to specific device types
- Consider compatibility and connectivity features

### Fashion Items
- Begin with "Clothing, Shoes & Accessories"
- Separate by gender and age group
- Consider seasonal and style categories

### Home Products
- Start with "Home & Garden"
- Categorize by room or function
- Consider style and material subcategories

## Common Category Mistakes

### Too Generic
- Avoid broad categories when specific ones exist
- Generic categories have more competition
- Specific categories often have better conversion rates

### Wrong Primary Function
- Don't categorize by secondary features
- Focus on the item's main purpose
- Consider how buyers would search for the item

### Ignoring Requirements
- Some categories require specific item specifics
- Certain categories have listing restrictions
- Professional categories may need certifications

## Category-Specific Tips

### Electronics
- Brand and model are usually required
- Connectivity options are important
- Condition descriptions must be accurate

### Fashion
- Size charts must be accurate
- Color descriptions should match photos
- Material composition may be required

### Automotive
- Compatibility information is crucial
- Part numbers should be accurate
- Installation requirements should be clear

## Using Category APIs

### GetCategories API
- Retrieve current category structure
- Get category IDs for API calls
- Check for category updates

### GetCategoryFeatures API
- Understand category-specific features
- Check listing format availability
- Review fee structures

### GetCategorySpecifics API
- Get required item specifics
- Understand accepted values
- Plan your product data mapping
EOD;
    }

    /**
     * Generate profile attributes guide content.
     */
    private function generateProfileAttributesGuide(): string
    {
        return <<<'EOD'
# Profile Attributes Configuration Guide

## Understanding Item Specifics

Item specifics are structured data fields that describe your products. They help buyers find your items and improve search visibility.

## Types of Item Specifics

### Required Specifics
These must be provided for successful listing:
- **Brand**: Product manufacturer or brand name
- **Condition**: New, Used, Refurbished, etc.
- **MPN**: Manufacturer Part Number (when available)

### Recommended Specifics
These improve visibility and buyer confidence:
- **Color**: Primary color of the item
- **Size**: Dimensions or size designation
- **Material**: Primary material composition
- **Style**: Design style or category

### Optional Specifics
Additional details that can help differentiate your product:
- **Features**: Special characteristics
- **Compatibility**: What the item works with
- **Warranty**: Warranty information

## Configuring Attributes in Profiles

### Mapping Shopify Fields
- Map Shopify product fields to eBay specifics
- Use product tags for additional attributes
- Set up custom field mappings

### Default Values
- Set default values for common attributes
- Use conditional logic for different product types
- Create fallback values for missing data

### Validation Rules
- Ensure values match eBay's accepted values
- Set up validation for required fields
- Create error handling for invalid data

## Category-Specific Attributes

### Electronics Categories
Common required attributes:
- Brand, Model, Connectivity
- Storage Capacity, Screen Size
- Operating System, Processor Type

### Fashion Categories
Common required attributes:
- Brand, Size, Color, Material
- Gender, Age Group, Season
- Style, Fit, Closure Type

### Home & Garden Categories
Common required attributes:
- Brand, Material, Color
- Room, Style, Dimensions
- Assembly Required, Care Instructions

## Best Practices

### Accuracy is Key
- Ensure all attributes accurately describe the product
- Use consistent terminology across similar products
- Verify measurements and specifications

### Completeness Matters
- Fill all required attributes
- Provide recommended attributes when possible
- Use specific values rather than generic ones

### Consistency Across Products
- Use the same attribute values for similar products
- Maintain consistent formatting and terminology
- Create standardized value lists

## Common Attribute Errors

### Missing Required Attributes
- **Problem**: Listings rejected due to missing required fields
- **Solution**: Check category requirements and ensure all required fields are mapped

### Incorrect Values
- **Problem**: Using values not accepted by eBay
- **Solution**: Use eBay's GetCategorySpecifics API to get accepted values

### Inconsistent Formatting
- **Problem**: Same attribute formatted differently across products
- **Solution**: Create standardized value lists and validation rules

## Advanced Attribute Configuration

### Conditional Logic
Set up rules that apply different attributes based on:
- Product type or category
- Price ranges
- Brand or manufacturer
- Seasonal considerations

### Dynamic Attributes
- Pull attributes from Shopify product data
- Use product tags for flexible attribute assignment
- Implement custom field mapping logic

### Bulk Attribute Management
- Update attributes across multiple products
- Import/export attribute configurations
- Validate attributes before listing

## Monitoring Attribute Performance

### Track Listing Success
- Monitor which attribute combinations perform best
- Identify attributes that improve search visibility
- Analyze conversion rates by attribute completeness

### Regular Audits
- Review attribute accuracy periodically
- Update attributes based on eBay changes
- Ensure compliance with category requirements
EOD;
    }

    /**
     * Generate inventory sync guide content.
     */
    private function generateInventorySyncGuide(): string
    {
        return <<<'EOD'
# Inventory Synchronization Guide

## Overview
Inventory synchronization ensures stock levels remain consistent between Shopify and eBay, preventing overselling and maintaining accurate availability.

## How Sync Works

### Real-Time Updates
- Shopify sales automatically reduce eBay quantities
- eBay sales automatically reduce Shopify inventory
- Manual adjustments sync between platforms

### Sync Frequency
- Critical updates (sales) sync within minutes
- Bulk updates occur every 15-30 minutes
- Manual sync available for immediate updates

## Configuration

### Basic Settings
- Enable/disable inventory sync
- Set sync direction (uni or bidirectional)
- Configure update frequency

### Advanced Options
- Low stock thresholds
- Out-of-stock behavior
- Reserved inventory amounts
- SKU-based matching rules

## Common Issues

### Sync Delays
- Check API rate limits
- Verify network connectivity
- Review sync queue status

### Inventory Mismatches
- Run manual reconciliation
- Check SKU mappings
- Review sync error logs

### Overselling Prevention
- Set inventory buffers
- Monitor low stock alerts
- Use reserved inventory settings

## Best Practices
- Use consistent SKU formats
- Monitor sync performance
- Set up low stock alerts
- Regular inventory audits
EOD;
    }

    /**
     * Generate product upload guide content.
     */
    private function generateProductUploadGuide(): string
    {
        return <<<'EOD'
# Product Upload Process Guide

## Upload Overview
The product upload process transfers your Shopify products to eBay as listings, applying profile configurations and ensuring compliance.

## Upload Process Steps

### 1. Product Selection
- Choose products to upload
- Verify product data completeness
- Check profile assignments

### 2. Data Validation
- Validate required fields
- Check category compatibility
- Verify item specifics

### 3. Image Processing
- Optimize images for eBay
- Ensure image quality standards
- Handle multiple product images

### 4. Listing Creation
- Apply profile settings
- Generate eBay-compliant descriptions
- Set pricing and inventory

### 5. Quality Checks
- Validate listing data
- Check policy compliance
- Verify category requirements

## Upload Methods

### Individual Upload
- Upload single products
- Full control over settings
- Ideal for testing and special items

### Bulk Upload
- Upload multiple products simultaneously
- Efficient for large catalogs
- Uses batch processing

### Automated Upload
- Automatic upload of new products
- Based on profile rules
- Scheduled or trigger-based

## Common Upload Issues

### Missing Required Data
- Incomplete product information
- Missing item specifics
- Invalid category assignments

### Image Problems
- Poor image quality
- Incorrect image formats
- Missing product images

### Policy Violations
- Prohibited items
- Incorrect categorization
- Missing required disclosures

## Upload Best Practices
- Complete all product data before upload
- Test with small batches first
- Monitor upload success rates
- Keep detailed upload logs
EOD;
    }

    /**
     * Generate order sync guide content.
     */
    private function generateOrderSyncGuide(): string
    {
        return <<<'EOD'
# Order Synchronization Guide

## Order Sync Overview
Order synchronization automatically imports eBay orders into Shopify for unified order management and fulfillment.

## Sync Process

### Order Detection
- eBay sends order notifications via webhooks
- System processes order data
- Validates order information

### Order Creation
- Creates corresponding Shopify order
- Maps eBay buyer to Shopify customer
- Transfers order line items and pricing

### Inventory Updates
- Reduces Shopify inventory levels
- Updates eBay listing quantities
- Handles variant-specific inventory

### Status Synchronization
- Syncs payment status
- Updates shipping information
- Handles order modifications

## Order Data Mapping

### Customer Information
- Maps eBay buyer details to Shopify customer
- Handles shipping addresses
- Manages contact information

### Product Mapping
- Links eBay items to Shopify products
- Maps variations correctly
- Handles SKU-based matching

### Pricing and Taxes
- Converts currencies if needed
- Maps eBay fees and taxes
- Handles promotional pricing

## Common Sync Issues

### Missing Orders
- Check webhook configuration
- Verify API permissions
- Review order filters

### Duplicate Orders
- Check order ID mapping
- Review sync timing
- Verify deduplication logic

### Inventory Discrepancies
- Validate SKU mappings
- Check inventory sync settings
- Review order processing logs

## Best Practices
- Monitor order sync regularly
- Set up sync failure alerts
- Maintain accurate SKU mappings
- Regular order reconciliation
EOD;
    }

    /**
     * Generate initial setup guide content.
     */
    private function generateInitialSetupGuide(): string
    {
        return <<<'EOD'
# Initial Setup and Configuration Guide

## Getting Started
This guide walks you through the initial setup of your eBay-Shopify integration.

## Prerequisites
- Active Shopify store
- eBay seller account
- Valid payment methods set up

## Step 1: eBay Authentication
1. Connect your eBay account
2. Grant necessary permissions
3. Verify account status and limits

## Step 2: Shopify Connection
1. Install the app from Shopify App Store
2. Grant required permissions
3. Verify store data access

## Step 3: Basic Configuration
1. Set default currency and location
2. Configure basic sync settings
3. Set up notification preferences

## Step 4: Create Your First Profile
1. Choose a product category
2. Configure item specifics
3. Set pricing rules
4. Test with a sample product

## Step 5: Initial Product Upload
1. Select test products
2. Apply profile settings
3. Review and upload
4. Monitor listing status

## Verification Steps
- Check eBay listings are created correctly
- Verify inventory sync is working
- Test order processing flow
- Confirm notification settings
EOD;
    }

    /**
     * Generate troubleshooting guide content.
     */
    private function generateTroubleshootingGuide(): string
    {
        return <<<'EOD'
# Common Issues and Troubleshooting Guide

## Authentication Issues

### eBay Token Expired
- **Symptoms**: API errors, sync failures
- **Solution**: Re-authenticate eBay account
- **Prevention**: Monitor token expiration dates

### Insufficient Permissions
- **Symptoms**: Permission denied errors
- **Solution**: Check and grant required permissions
- **Prevention**: Regular permission audits

## Sync Problems

### Products Not Uploading
- **Causes**: Missing data, category issues, policy violations
- **Solutions**: Validate product data, check categories, review policies
- **Prevention**: Use data validation before upload

### Inventory Mismatches
- **Causes**: Sync delays, SKU issues, manual changes
- **Solutions**: Manual reconciliation, fix SKU mappings, check sync logs
- **Prevention**: Consistent SKU management

### Order Sync Failures
- **Causes**: Webhook issues, data validation, API limits
- **Solutions**: Check webhooks, validate data, monitor API usage
- **Prevention**: Regular monitoring and alerts

## Performance Issues

### Slow Sync Times
- **Causes**: Large catalogs, API limits, network issues
- **Solutions**: Batch processing, optimize sync frequency, check connectivity
- **Prevention**: Performance monitoring

### High Error Rates
- **Causes**: Data quality, configuration issues, API changes
- **Solutions**: Improve data quality, review configuration, update integrations
- **Prevention**: Regular audits and updates

## Data Quality Issues

### Missing Product Information
- **Causes**: Incomplete Shopify data, mapping issues
- **Solutions**: Complete product data, fix mappings, set defaults
- **Prevention**: Data quality checks

### Incorrect Categorization
- **Causes**: Wrong category selection, outdated mappings
- **Solutions**: Research correct categories, update mappings
- **Prevention**: Regular category reviews

## Getting Help
- Check error logs and messages
- Use diagnostic tools
- Contact support with detailed information
- Join community forums for tips
EOD;
    }

    /**
     * Generate Shopify platform guide content.
     */
    private function generateShopifyPlatformGuide(): string
    {
        return <<<'EOD'
# Shopify Platform Integration Guide

## Shopify API Overview
The integration uses Shopify's REST and GraphQL APIs to access store data and manage products.

## Key Shopify Concepts

### Products and Variants
- Products are the main items in your store
- Variants represent different options (size, color, etc.)
- Each variant has its own SKU and inventory

### Collections
- Group related products together
- Can be manual or automatic based on rules
- Used for organization and navigation

### Inventory Management
- Track quantities across multiple locations
- Support for inventory policies and tracking
- Integration with fulfillment services

## API Integration Points

### Product Data Sync
- Retrieve product information
- Monitor product changes via webhooks
- Handle product variants and options

### Inventory Management
- Real-time inventory updates
- Multi-location inventory support
- Low stock notifications

### Order Processing
- Create orders from eBay sales
- Update order status and tracking
- Handle refunds and cancellations

## Shopify Webhooks
- Product creation/updates
- Inventory level changes
- Order status updates
- App uninstall notifications

## Best Practices
- Use GraphQL for efficient data retrieval
- Implement proper webhook handling
- Handle API rate limits gracefully
- Maintain data consistency
EOD;
    }

    /**
     * Generate eBay platform guide content.
     */
    private function generateEbayPlatformGuide(): string
    {
        return <<<'EOD'
# eBay Platform Integration Guide

## eBay API Overview
The integration uses eBay's Trading API, Inventory API, and other specialized APIs for comprehensive marketplace management.

## Key eBay Concepts

### Listings and Items
- Fixed-price listings vs auction-style
- Item specifics and categories
- Listing policies and requirements

### Inventory Management
- SKU-based inventory tracking
- Multi-variation listings
- Quantity and pricing updates

### Order Management
- Order processing workflow
- Payment and shipping handling
- Buyer communication

## API Integration Points

### Trading API
- Create and manage listings
- Retrieve order information
- Handle buyer messages

### Inventory API
- Modern inventory management
- Bulk operations support
- Advanced listing features

### Compliance API
- Policy violation detection
- Listing quality assessment
- Regulatory compliance

## eBay Categories
- Hierarchical category structure
- Category-specific requirements
- Item specifics by category

## Listing Policies
- Payment policies
- Return policies
- Shipping policies
- Business policies

## Best Practices
- Follow eBay's listing policies
- Use appropriate categories
- Complete all required item specifics
- Maintain good seller metrics
EOD;
    }
}
