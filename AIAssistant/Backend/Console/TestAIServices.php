<?php

namespace App\Console\Commands;

use App\Services\AI\LLM\OpenAIService;
use App\Services\AI\VectorStore\PineconeService;
use Illuminate\Console\Command;

class TestAIServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:test-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test AI services (OpenAI, Pinecone)';

    private OpenAIService $openAIService;
    private PineconeService $pineconeService;

    public function __construct(OpenAIService $openAIService, PineconeService $pineconeService)
    {
        parent::__construct();
        $this->openAIService = $openAIService;
        $this->pineconeService = $pineconeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing AI Services');
        $this->newLine();

        // Test OpenAI Configuration
        $this->info('🔧 Testing OpenAI Configuration...');
        if ($this->openAIService->validateConfiguration()) {
            $this->info('✅ OpenAI configuration is valid');
        } else {
            $this->error('❌ OpenAI configuration is invalid');
            return Command::FAILURE;
        }

        // Test Pinecone Configuration
        $this->info('🔧 Testing Pinecone Configuration...');
        if ($this->pineconeService->validateConfiguration()) {
            $this->info('✅ Pinecone configuration is valid');
        } else {
            $this->error('❌ Pinecone configuration is invalid');
            return Command::FAILURE;
        }

        // Test OpenAI Embedding
        $this->info('🔤 Testing OpenAI Embedding Generation...');
        $embeddingResult = $this->openAIService->generateEmbedding('Test embedding for eBay-Shopify integration');

        if ($embeddingResult['success']) {
            $this->info('✅ OpenAI embedding generation successful');
            $this->line('   - Embedding dimension: ' . count($embeddingResult['embedding']));
            $this->line('   - Tokens used: ' . $embeddingResult['usage']['total_tokens']);
        } else {
            $this->error('❌ OpenAI embedding generation failed: ' . $embeddingResult['error']);
            return Command::FAILURE;
        }

        // Test OpenAI Chat
        $this->info('💬 Testing OpenAI Chat Completion...');
        $messages = [
            ['role' => 'system', 'content' => 'You are a helpful assistant for eBay-Shopify integration.'],
            ['role' => 'user', 'content' => 'What is eBay category selection?']
        ];

        $chatResult = $this->openAIService->chat($messages);

        if ($chatResult['success']) {
            $this->info('✅ OpenAI chat completion successful');
            $this->line('   - Response length: ' . strlen($chatResult['content']));
            $this->line('   - Tokens used: ' . $chatResult['usage']['total_tokens']);
            $this->line('   - Response preview: ' . substr($chatResult['content'], 0, 100) . '...');
        } else {
            $this->error('❌ OpenAI chat completion failed: ' . $chatResult['error']);
            return Command::FAILURE;
        }

        // Test Pinecone Vector Operations
        $this->info('🔍 Testing Pinecone Vector Operations...');
        
        // Create a test vector
        $testVector = $this->pineconeService->formatVector(
            'test_vector_' . time(),
            $embeddingResult['embedding'],
            [
                'test' => true,
                'category' => 'test',
                'content' => 'Test vector for AI services validation'
            ]
        );

        // Test vector upsert
        if ($this->pineconeService->upsertVectors([$testVector])) {
            $this->info('✅ Pinecone vector upsert successful');
        } else {
            $this->error('❌ Pinecone vector upsert failed');
            return Command::FAILURE;
        }

        // Test vector search
        $searchResult = $this->pineconeService->search(
            $embeddingResult['embedding'],
            5,
            ['test' => true]
        );

        if ($searchResult['success']) {
            $this->info('✅ Pinecone vector search successful');
            $this->line('   - Results found: ' . count($searchResult['matches']));
            if (!empty($searchResult['matches'])) {
                $this->line('   - Top match score: ' . $searchResult['matches'][0]['score']);
            }
        } else {
            $this->error('❌ Pinecone vector search failed: ' . $searchResult['error']);
            return Command::FAILURE;
        }

        // Clean up test vector
        $this->info('🧹 Cleaning up test data...');
        if ($this->pineconeService->deleteVectors([$testVector['id']])) {
            $this->info('✅ Test vector cleanup successful');
        } else {
            $this->warn('⚠️ Test vector cleanup failed (this is not critical)');
        }

        $this->newLine();
        $this->info('🎉 All AI services tests passed successfully!');
        
        return Command::SUCCESS;
    }
}
