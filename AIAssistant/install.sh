#!/bin/bash

# AI Assistant Module Installation Script
# This script installs the AI Assistant module into your Laravel application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Laravel installation
check_laravel() {
    if [ ! -f "artisan" ]; then
        print_error "This script must be run from the root of a Laravel application"
        exit 1
    fi
    print_success "Laravel application detected"
}

# Function to backup existing files
backup_files() {
    print_status "Creating backup of existing files..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup files that might be overwritten
    if [ -f "config/ai_assistant.php" ]; then
        cp "config/ai_assistant.php" "$BACKUP_DIR/"
        print_warning "Backed up existing config/ai_assistant.php"
    fi
    
    if [ -f "app/Providers/AIAssistantServiceProvider.php" ]; then
        cp "app/Providers/AIAssistantServiceProvider.php" "$BACKUP_DIR/"
        print_warning "Backed up existing AIAssistantServiceProvider.php"
    fi
    
    print_success "Backup created in $BACKUP_DIR"
}

# Function to copy backend files
install_backend() {
    print_status "Installing backend components..."
    
    # Create directories
    mkdir -p app/Models/AI
    mkdir -p app/Services/AI/LLM
    mkdir -p app/Services/AI/VectorStore
    mkdir -p app/Services/AI/Knowledge
    mkdir -p app/Http/Controllers/AI
    mkdir -p storage/ai_knowledge
    
    # Copy models
    cp -r AIAssistant/Backend/Models/* app/Models/AI/
    print_success "Models installed"
    
    # Copy services
    cp -r AIAssistant/Backend/Services/LLM/* app/Services/AI/LLM/
    cp -r AIAssistant/Backend/Services/VectorStore/* app/Services/AI/VectorStore/
    cp -r AIAssistant/Backend/Services/Knowledge/* app/Services/AI/Knowledge/
    cp AIAssistant/Backend/Services/TitleGeneratorService.php app/Services/AI/
    print_success "Services installed"
    
    # Copy controllers
    cp -r AIAssistant/Backend/Controllers/AI/* app/Http/Controllers/AI/
    print_success "Controllers installed"
    
    # Copy console commands
    cp -r AIAssistant/Backend/Console/* app/Console/Commands/
    print_success "Console commands installed"
    
    # Copy service provider
    cp AIAssistant/Backend/Providers/AIAssistantServiceProvider.php app/Providers/
    print_success "Service provider installed"
    
    # Copy configuration
    cp AIAssistant/Backend/Config/ai_assistant.php config/
    print_success "Configuration installed"
    
    # Copy migrations
    cp -r AIAssistant/Backend/Database/Migrations/* database/migrations/
    print_success "Migrations installed"
    
    # Copy knowledge base
    cp -r AIAssistant/Backend/Storage/KnowledgeBase/* storage/ai_knowledge/
    print_success "Knowledge base installed"
}

# Function to update configuration files
update_config() {
    print_status "Updating configuration files..."
    
    # Add service provider to config/app.php
    if ! grep -q "AIAssistantServiceProvider" config/app.php; then
        # Find the providers array and add our provider
        sed -i.bak '/App\\Providers\\RouteServiceProvider::class,/a\
        App\\Providers\\AIAssistantServiceProvider::class,' config/app.php
        print_success "Added AIAssistantServiceProvider to config/app.php"
    else
        print_warning "AIAssistantServiceProvider already exists in config/app.php"
    fi
}

# Function to update routes
update_routes() {
    print_status "Setting up routes..."
    
    # Add AI Assistant routes to api.php
    if [ -f "AIAssistant/Routes/ai_assistant.php" ]; then
        echo "" >> routes/api.php
        echo "// AI Assistant Routes" >> routes/api.php
        echo "require __DIR__.'/ai_assistant.php';" >> routes/api.php
        
        # Copy the routes file
        cp AIAssistant/Routes/ai_assistant.php routes/
        print_success "AI Assistant routes added"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Checking dependencies..."
    
    # Check if composer is available
    if command_exists composer; then
        print_status "Running composer install..."
        composer install --no-dev --optimize-autoloader
        print_success "Composer dependencies installed"
    else
        print_warning "Composer not found. Please run 'composer install' manually"
    fi
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if command_exists php; then
        php artisan migrate --force
        print_success "Database migrations completed"
    else
        print_warning "PHP not found. Please run 'php artisan migrate' manually"
    fi
}

# Function to setup environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found. Please create one from .env.example"
        exit 1
    fi
    
    # Check if AI Assistant variables already exist
    if ! grep -q "OPENAI_API_KEY" .env; then
        print_status "Adding AI Assistant environment variables..."
        echo "" >> .env
        echo "# AI Assistant Configuration" >> .env
        cat AIAssistant/.env.example >> .env
        print_success "Environment variables added to .env"
        print_warning "Please configure your OpenAI and Pinecone API keys in .env"
    else
        print_warning "AI Assistant environment variables already exist in .env"
    fi
}

# Function to clear caches
clear_caches() {
    print_status "Clearing application caches..."
    
    if command_exists php; then
        php artisan config:clear
        php artisan cache:clear
        php artisan route:clear
        php artisan view:clear
        print_success "Caches cleared"
    else
        print_warning "PHP not found. Please clear caches manually"
    fi
}

# Function to run tests
run_tests() {
    if [ "$1" = "--with-tests" ]; then
        print_status "Installing test files..."
        
        mkdir -p tests/Unit/AIAssistant
        mkdir -p tests/Feature/AIAssistant
        
        cp -r AIAssistant/Tests/Unit/* tests/Unit/AIAssistant/
        cp -r AIAssistant/Tests/Feature/* tests/Feature/AIAssistant/
        
        print_success "Test files installed"
        
        if command_exists php; then
            print_status "Running AI Assistant tests..."
            php artisan test --testsuite=AIAssistant
        fi
    fi
}

# Function to display post-installation instructions
show_instructions() {
    print_success "AI Assistant module installation completed!"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Configure your API keys in .env file:"
    echo "   - OPENAI_API_KEY=your_openai_api_key"
    echo "   - PINECONE_API_KEY=your_pinecone_api_key"
    echo "   - PINECONE_HOST=your_pinecone_host_url"
    echo ""
    echo "2. Test your AI services:"
    echo "   php artisan ai:test-services"
    echo ""
    echo "3. Ingest knowledge base:"
    echo "   php artisan ai:ingest-knowledge"
    echo ""
    echo "4. Test OpenAI models (optional):"
    echo "   php artisan ai:test-models"
    echo ""
    echo -e "${GREEN}Documentation:${NC} See AIAssistant/README.md for detailed usage instructions"
    echo ""
}

# Main installation function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                AI Assistant Module Installer                 ║"
    echo "║              eBay-Shopify Integration App                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Check prerequisites
    check_laravel
    
    # Create backup
    backup_files
    
    # Install components
    install_backend
    update_config
    update_routes
    setup_environment
    install_dependencies
    run_migrations
    clear_caches
    
    # Run tests if requested
    run_tests "$1"
    
    # Show completion message
    show_instructions
}

# Run main function with all arguments
main "$@"
