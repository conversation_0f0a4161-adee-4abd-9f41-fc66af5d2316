#!/bin/bash

# AI Assistant Module Verification Script
# This script verifies that all components are properly packaged

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to print colored output
print_check() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if [ -f "$1" ] || [ -d "$1" ]; then
        echo -e "${GREEN}✓${NC} $2"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} $2 (Missing: $1)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Main verification function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              AI Assistant Module Verification                ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Check if we're in the right directory
    if [ ! -d "AIAssistant" ]; then
        echo -e "${RED}Error: AIAssistant directory not found. Please run this script from the project root.${NC}"
        exit 1
    fi
    
    cd AIAssistant
    
    print_section "Core Documentation"
    print_check "README.md" "Main documentation"
    print_check "INSTALLATION_SUMMARY.md" "Installation summary"
    print_check ".env.example" "Environment configuration template"
    print_check "install.sh" "Installation script"
    print_check "verify.sh" "Verification script"
    
    print_section "Backend Models"
    print_check "Backend/Models/AIConversation.php" "AI Conversation model"
    print_check "Backend/Models/AIMessage.php" "AI Message model"
    print_check "Backend/Models/AIKnowledgeDocument.php" "Knowledge Document model"
    print_check "Backend/Models/AIKnowledgeChunk.php" "Knowledge Chunk model"
    print_check "Backend/Models/AITaskExecution.php" "Task Execution model"
    
    print_section "Backend Services"
    print_check "Backend/Services/LLM/OpenAIService.php" "OpenAI service"
    print_check "Backend/Services/VectorStore/PineconeService.php" "Pinecone service"
    print_check "Backend/Services/Knowledge/DocumentProcessor.php" "Document processor"
    print_check "Backend/Services/Knowledge/KnowledgeIngestionService.php" "Knowledge ingestion service"
    print_check "Backend/Services/TitleGeneratorService.php" "Title generator service"
    
    print_section "Backend Controllers"
    print_check "Backend/Controllers/AI/TitleSuggestionController.php" "Title suggestion controller"
    
    print_section "Console Commands"
    print_check "Backend/Console/IngestKnowledgeBase.php" "Knowledge ingestion command"
    print_check "Backend/Console/TestAIServices.php" "AI services test command"
    print_check "Backend/Console/TestOpenAIModels.php" "OpenAI models test command"
    
    print_section "Configuration & Infrastructure"
    print_check "Backend/Providers/AIAssistantServiceProvider.php" "Service provider"
    print_check "Backend/Config/ai_assistant.php" "Configuration file"
    print_check "Backend/Database/Migrations/2024_01_15_000001_create_ai_assistant_tables.php" "Database migration"
    
    print_section "Knowledge Base Content"
    print_check "Backend/Storage/KnowledgeBase/app_domain/profile_management.md" "Profile management guide"
    print_check "Backend/Storage/KnowledgeBase/app_domain/inventory_sync.md" "Inventory sync guide"
    print_check "Backend/Storage/KnowledgeBase/ebay_platform/category_hierarchy.md" "eBay category guide"
    print_check "Backend/Storage/KnowledgeBase/troubleshooting/sync_issues.md" "Troubleshooting guide"
    
    print_section "Routes"
    print_check "Routes/ai_assistant.php" "AI Assistant routes"
    
    print_section "Tests"
    print_check "Tests/Unit/OpenAIServiceTest.php" "OpenAI service unit tests"
    print_check "Tests/Unit/PineconeServiceTest.php" "Pinecone service unit tests"
    print_check "Tests/Feature/KnowledgeIngestionTest.php" "Knowledge ingestion feature tests"
    
    # Check file permissions
    print_section "File Permissions"
    if [ -x "install.sh" ]; then
        echo -e "${GREEN}✓${NC} Installation script is executable"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} Installation script is not executable"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # Check file sizes (basic validation)
    print_section "File Size Validation"
    
    # Check that key files are not empty
    key_files=(
        "README.md"
        "Backend/Services/LLM/OpenAIService.php"
        "Backend/Services/VectorStore/PineconeService.php"
        "Backend/Config/ai_assistant.php"
        "Backend/Database/Migrations/2024_01_15_000001_create_ai_assistant_tables.php"
    )
    
    for file in "${key_files[@]}"; do
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if [ -f "$file" ] && [ -s "$file" ]; then
            echo -e "${GREEN}✓${NC} $file has content"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${RED}✗${NC} $file is empty or missing"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    done
    
    # Summary
    print_section "Verification Summary"
    echo -e "Total checks: ${BLUE}$TOTAL_CHECKS${NC}"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All verification checks passed!${NC}"
        echo -e "${GREEN}The AI Assistant module is properly packaged and ready for installation.${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some verification checks failed.${NC}"
        echo -e "${RED}Please review the missing components before installation.${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
