<?php

namespace Tests\Feature\AIAssistant;

use Tests\TestCase;
use App\Models\AI\AIKnowledgeDocument;
use App\Models\AI\AIKnowledgeChunk;
use App\Services\AI\Knowledge\KnowledgeIngestionService;
use App\Services\AI\Knowledge\DocumentProcessor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

class KnowledgeIngestionTest extends TestCase
{
    use RefreshDatabase;

    private KnowledgeIngestionService $ingestionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test storage
        Storage::fake('local');
        
        $this->ingestionService = app(KnowledgeIngestionService::class);
    }

    public function test_ingest_from_file_success()
    {
        // Create a test markdown file
        $content = "# Test Document\n\nThis is a test document for knowledge ingestion.";
        Storage::put('test_knowledge/test_document.md', $content);

        $document = $this->ingestionService->ingestFromFile(
            'test_knowledge/test_document.md',
            'app_domain',
            'test_category',
            ['test', 'knowledge']
        );

        $this->assertNotNull($document);
        $this->assertEquals('Test Document', $document->title);
        $this->assertEquals('app_domain', $document->category);
        $this->assertEquals('test_category', $document->subcategory);
        $this->assertEquals(['test', 'knowledge'], $document->tags);
        $this->assertTrue($document->is_active);
    }

    public function test_ingest_from_file_creates_chunks()
    {
        // Create a longer test document
        $content = str_repeat("This is a test paragraph with enough content to create multiple chunks. ", 100);
        Storage::put('test_knowledge/long_document.md', "# Long Document\n\n" . $content);

        $document = $this->ingestionService->ingestFromFile(
            'test_knowledge/long_document.md',
            'app_domain',
            'test_category'
        );

        $this->assertNotNull($document);
        $this->assertGreaterThan(0, $document->chunks()->count());
        
        // Verify chunks have proper structure
        $firstChunk = $document->chunks()->first();
        $this->assertNotNull($firstChunk->content);
        $this->assertNotNull($firstChunk->pinecone_vector_id);
        $this->assertEquals(0, $firstChunk->chunk_index);
    }

    public function test_ingest_from_directory()
    {
        // Create multiple test files
        Storage::put('test_knowledge/doc1.md', "# Document 1\n\nContent for document 1");
        Storage::put('test_knowledge/doc2.md', "# Document 2\n\nContent for document 2");
        Storage::put('test_knowledge/doc3.txt', "Document 3 content");
        Storage::put('test_knowledge/ignored.pdf', "This should be ignored");

        $documents = $this->ingestionService->ingestFromDirectory(
            'test_knowledge',
            'app_domain',
            'test_category'
        );

        // Should process 3 text files, ignore PDF
        $this->assertCount(3, $documents);
        
        $titles = collect($documents)->pluck('title')->toArray();
        $this->assertContains('Document 1', $titles);
        $this->assertContains('Document 2', $titles);
        $this->assertContains('Doc3', $titles);
    }

    public function test_duplicate_content_detection()
    {
        $content = "# Test Document\n\nThis is a test document.";
        Storage::put('test_knowledge/test1.md', $content);
        Storage::put('test_knowledge/test2.md', $content); // Same content

        // Ingest first document
        $document1 = $this->ingestionService->ingestFromFile(
            'test_knowledge/test1.md',
            'app_domain'
        );

        // Attempt to ingest duplicate content
        $document2 = $this->ingestionService->ingestFromFile(
            'test_knowledge/test2.md',
            'app_domain'
        );

        $this->assertNotNull($document1);
        $this->assertNotNull($document2);
        $this->assertEquals($document1->id, $document2->id); // Should return existing document
    }

    public function test_ingest_app_domain_knowledge()
    {
        $documents = $this->ingestionService->ingestAppDomainKnowledge();

        $this->assertNotEmpty($documents);
        
        // Check that programmatically generated documents are created
        $titles = collect($documents)->pluck('title')->toArray();
        $this->assertContains('eBay Profile Creation Guide', $titles);
        $this->assertContains('eBay Category Selection Guide', $titles);
        $this->assertContains('Profile Attributes Configuration Guide', $titles);
    }

    public function test_ingest_platform_knowledge()
    {
        $documents = $this->ingestionService->ingestPlatformKnowledge();

        $this->assertNotEmpty($documents);
        
        // Check that platform-specific documents are created
        $titles = collect($documents)->pluck('title')->toArray();
        $this->assertContains('Shopify Platform Integration Guide', $titles);
        $this->assertContains('eBay Platform Integration Guide', $titles);
    }

    public function test_file_not_found_handling()
    {
        $document = $this->ingestionService->ingestFromFile(
            'nonexistent/file.md',
            'app_domain'
        );

        $this->assertNull($document);
    }

    public function test_invalid_file_type_ignored()
    {
        Storage::put('test_knowledge/image.jpg', 'fake image content');

        $documents = $this->ingestionService->ingestFromDirectory(
            'test_knowledge',
            'app_domain'
        );

        $this->assertEmpty($documents);
    }

    public function test_title_extraction_from_path()
    {
        $content = "Some content without a title";
        Storage::put('test_knowledge/my_test_document.md', $content);

        $document = $this->ingestionService->ingestFromFile(
            'test_knowledge/my_test_document.md',
            'app_domain'
        );

        $this->assertNotNull($document);
        $this->assertEquals('My Test Document', $document->title);
    }

    public function test_content_hash_generation()
    {
        $content = "# Test Document\n\nUnique content for hash testing";
        Storage::put('test_knowledge/hash_test.md', $content);

        $document = $this->ingestionService->ingestFromFile(
            'test_knowledge/hash_test.md',
            'app_domain'
        );

        $this->assertNotNull($document);
        $this->assertNotNull($document->content_hash);
        $this->assertEquals(hash('sha256', $content), $document->content_hash);
    }

    public function test_embedding_metadata_storage()
    {
        $content = "# Test Document\n\nContent for embedding metadata testing";
        Storage::put('test_knowledge/metadata_test.md', $content);

        $document = $this->ingestionService->ingestFromFile(
            'test_knowledge/metadata_test.md',
            'app_domain'
        );

        $this->assertNotNull($document);
        $this->assertNotNull($document->embedding_metadata);
        $this->assertArrayHasKey('chunks_count', $document->embedding_metadata);
        $this->assertArrayHasKey('processed_at', $document->embedding_metadata);
    }
}
