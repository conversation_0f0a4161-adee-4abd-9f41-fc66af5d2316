<?php

namespace Tests\Unit\AIAssistant;

use Tests\TestCase;
use App\Services\AI\VectorStore\PineconeService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

class PineconeServiceTest extends TestCase
{
    private PineconeService $pineconeService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock configuration
        Config::set('ai_assistant.pinecone', [
            'api_key' => 'test-api-key',
            'host' => 'https://test-index.svc.test.pinecone.io',
            'index_name' => 'test-index',
            'dimension' => 1536,
            'metric' => 'cosine',
            'timeout' => 30,
        ]);
        
        $this->pineconeService = new PineconeService();
    }

    public function test_vector_upsert_success()
    {
        // Mock successful upsert response
        Http::fake([
            '*/vectors/upsert' => Http::response([
                'upsertedCount' => 1
            ], 200)
        ]);

        $vectors = [
            [
                'id' => 'test-vector-1',
                'values' => array_fill(0, 1536, 0.1),
                'metadata' => [
                    'category' => 'test',
                    'content' => 'Test content'
                ]
            ]
        ];

        $result = $this->pineconeService->upsertVectors($vectors);

        $this->assertTrue($result);
    }

    public function test_vector_search_success()
    {
        // Mock successful search response
        Http::fake([
            '*/query' => Http::response([
                'matches' => [
                    [
                        'id' => 'test-vector-1',
                        'score' => 0.95,
                        'metadata' => [
                            'category' => 'test',
                            'content' => 'Test content'
                        ]
                    ],
                    [
                        'id' => 'test-vector-2',
                        'score' => 0.87,
                        'metadata' => [
                            'category' => 'test',
                            'content' => 'Another test content'
                        ]
                    ]
                ]
            ], 200)
        ]);

        $queryVector = array_fill(0, 1536, 0.1);
        $result = $this->pineconeService->search($queryVector, 5);

        $this->assertTrue($result['success']);
        $this->assertCount(2, $result['matches']);
        $this->assertEquals(0.95, $result['matches'][0]['score']);
    }

    public function test_vector_search_with_filter()
    {
        // Mock successful search response with filter
        Http::fake([
            '*/query' => Http::response([
                'matches' => [
                    [
                        'id' => 'test-vector-1',
                        'score' => 0.95,
                        'metadata' => [
                            'category' => 'app_domain',
                            'content' => 'Test content'
                        ]
                    ]
                ]
            ], 200)
        ]);

        $queryVector = array_fill(0, 1536, 0.1);
        $filter = ['category' => 'app_domain'];
        $result = $this->pineconeService->search($queryVector, 5, $filter);

        $this->assertTrue($result['success']);
        $this->assertCount(1, $result['matches']);
        $this->assertEquals('app_domain', $result['matches'][0]['metadata']['category']);
    }

    public function test_vector_deletion_success()
    {
        // Mock successful deletion response
        Http::fake([
            '*/vectors/delete' => Http::response([], 200)
        ]);

        $vectorIds = ['test-vector-1', 'test-vector-2'];
        $result = $this->pineconeService->deleteVectors($vectorIds);

        $this->assertTrue($result);
    }

    public function test_vector_deletion_by_filter()
    {
        // Mock successful deletion by filter response
        Http::fake([
            '*/vectors/delete' => Http::response([], 200)
        ]);

        $filter = ['category' => 'test'];
        $result = $this->pineconeService->deleteByFilter($filter);

        $this->assertTrue($result);
    }

    public function test_api_error_handling()
    {
        // Mock API error response
        Http::fake([
            '*/query' => Http::response([
                'error' => [
                    'message' => 'Invalid API key',
                    'code' => 'UNAUTHENTICATED'
                ]
            ], 401)
        ]);

        $queryVector = array_fill(0, 1536, 0.1);
        $result = $this->pineconeService->search($queryVector);

        $this->assertFalse($result['success']);
        $this->assertStringContains('Pinecone API error', $result['error']);
        $this->assertEmpty($result['matches']);
    }

    public function test_configuration_validation()
    {
        // Test with valid configuration
        $this->assertTrue($this->pineconeService->validateConfiguration());

        // Test with missing API key
        Config::set('ai_assistant.pinecone.api_key', '');
        $invalidService = new PineconeService();
        $this->assertFalse($invalidService->validateConfiguration());

        // Test with missing host and environment
        Config::set('ai_assistant.pinecone.api_key', 'test-key');
        Config::set('ai_assistant.pinecone.host', '');
        Config::set('ai_assistant.pinecone.environment', '');
        $invalidService2 = new PineconeService();
        $this->assertFalse($invalidService2->validateConfiguration());
    }

    public function test_vector_formatting()
    {
        $id = 'test-vector-1';
        $values = array_fill(0, 1536, 0.1);
        $metadata = ['category' => 'test', 'content' => 'Test content'];

        $formattedVector = $this->pineconeService->formatVector($id, $values, $metadata);

        $this->assertEquals($id, $formattedVector['id']);
        $this->assertEquals($values, $formattedVector['values']);
        $this->assertEquals($metadata, $formattedVector['metadata']);
    }

    public function test_vector_id_generation()
    {
        $vectorId1 = $this->pineconeService->generateVectorId('doc');
        $vectorId2 = $this->pineconeService->generateVectorId('chunk');

        $this->assertStringStartsWith('doc_', $vectorId1);
        $this->assertStringStartsWith('chunk_', $vectorId2);
        $this->assertNotEquals($vectorId1, $vectorId2);
    }

    public function test_index_stats()
    {
        $stats = $this->pineconeService->getIndexStats();

        $this->assertTrue($stats['success']);
        $this->assertArrayHasKey('stats', $stats);
        $this->assertArrayHasKey('totalVectorCount', $stats['stats']);
        $this->assertArrayHasKey('dimension', $stats['stats']);
    }
}
