<?php

namespace Tests\Unit\AIAssistant;

use Tests\TestCase;
use App\Services\AI\LLM\OpenAIService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

class OpenAIServiceTest extends TestCase
{
    private OpenAIService $openAIService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock configuration
        Config::set('ai_assistant.openai', [
            'api_key' => 'test-api-key',
            'model' => 'gpt-3.5-turbo',
            'embedding_model' => 'text-embedding-3-small',
            'max_tokens' => 4000,
            'temperature' => 0.7,
            'timeout' => 60,
        ]);
        
        $this->openAIService = new OpenAIService();
    }

    public function test_chat_completion_success()
    {
        // Mock successful API response
        Http::fake([
            'api.openai.com/v1/chat/completions' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Test response from AI assistant',
                            'role' => 'assistant'
                        ],
                        'finish_reason' => 'stop'
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 10,
                    'completion_tokens' => 20,
                    'total_tokens' => 30
                ]
            ], 200)
        ]);

        $messages = [
            ['role' => 'system', 'content' => 'You are a helpful assistant.'],
            ['role' => 'user', 'content' => 'Hello, how can you help me?']
        ];

        $result = $this->openAIService->chat($messages);

        $this->assertTrue($result['success']);
        $this->assertEquals('Test response from AI assistant', $result['content']);
        $this->assertEquals(30, $result['usage']['total_tokens']);
    }

    public function test_chat_completion_with_functions()
    {
        // Mock successful API response with function call
        Http::fake([
            'api.openai.com/v1/chat/completions' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => null,
                            'role' => 'assistant',
                            'function_call' => [
                                'name' => 'get_recommended_categories',
                                'arguments' => '{"product_type": "smartphone"}'
                            ]
                        ],
                        'finish_reason' => 'function_call'
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 15,
                    'completion_tokens' => 25,
                    'total_tokens' => 40
                ]
            ], 200)
        ]);

        $messages = [
            ['role' => 'user', 'content' => 'What category should I use for a smartphone?']
        ];

        $functions = [
            [
                'name' => 'get_recommended_categories',
                'description' => 'Get recommended eBay categories for a product',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'product_type' => ['type' => 'string']
                    ]
                ]
            ]
        ];

        $result = $this->openAIService->chat($messages, $functions);

        $this->assertTrue($result['success']);
        $this->assertNotNull($result['function_call']);
        $this->assertEquals('get_recommended_categories', $result['function_call']['name']);
    }

    public function test_embedding_generation_success()
    {
        // Mock successful embedding response
        Http::fake([
            'api.openai.com/v1/embeddings' => Http::response([
                'data' => [
                    [
                        'embedding' => array_fill(0, 1536, 0.1) // Mock embedding vector
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 5,
                    'total_tokens' => 5
                ]
            ], 200)
        ]);

        $text = 'Test text for embedding generation';
        $result = $this->openAIService->generateEmbedding($text);

        $this->assertTrue($result['success']);
        $this->assertIsArray($result['embedding']);
        $this->assertCount(1536, $result['embedding']);
        $this->assertEquals(5, $result['usage']['total_tokens']);
    }

    public function test_api_error_handling()
    {
        // Mock API error response
        Http::fake([
            'api.openai.com/v1/chat/completions' => Http::response([
                'error' => [
                    'message' => 'Invalid API key',
                    'type' => 'invalid_request_error'
                ]
            ], 401)
        ]);

        $messages = [
            ['role' => 'user', 'content' => 'Test message']
        ];

        $result = $this->openAIService->chat($messages);

        $this->assertFalse($result['success']);
        $this->assertStringContains('OpenAI API error', $result['error']);
    }

    public function test_configuration_validation()
    {
        // Test with valid configuration
        $this->assertTrue($this->openAIService->validateConfiguration());

        // Test with invalid configuration
        Config::set('ai_assistant.openai.api_key', '');
        Http::fake([
            'api.openai.com/v1/models' => Http::response([], 401)
        ]);

        $invalidService = new OpenAIService();
        $this->assertFalse($invalidService->validateConfiguration());
    }

    public function test_token_estimation()
    {
        $text = 'This is a test text for token estimation';
        $estimatedTokens = $this->openAIService->estimateTokenCount($text);

        $this->assertIsInt($estimatedTokens);
        $this->assertGreaterThan(0, $estimatedTokens);
    }

    public function test_token_limit_check()
    {
        $shortText = 'Short text';
        $longText = str_repeat('This is a very long text. ', 1000);

        $this->assertFalse($this->openAIService->exceedsTokenLimit($shortText));
        $this->assertTrue($this->openAIService->exceedsTokenLimit($longText, 100));
    }
}
