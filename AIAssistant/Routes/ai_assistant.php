<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AI\AIAssistantController;
use App\Http\Controllers\AI\KnowledgeBaseController;
use App\Http\Controllers\AI\ConversationController;

/*
|--------------------------------------------------------------------------
| AI Assistant Routes
|--------------------------------------------------------------------------
|
| Here are the routes for the AI Assistant functionality including
| chat interface, knowledge base management, and conversation handling.
|
*/

// AI Assistant Chat Routes
Route::middleware(['auth:shopify'])->prefix('ai-assistant')->group(function () {
    
    // Chat Interface
    Route::post('chat', [AIAssistantController::class, 'chat'])->name('ai.chat');
    Route::post('chat/stream', [AIAssistantController::class, 'streamChat'])->name('ai.chat.stream');
    
    // Conversation Management
    Route::get('conversations', [ConversationController::class, 'index'])->name('ai.conversations.index');
    Route::post('conversations', [ConversationController::class, 'store'])->name('ai.conversations.store');
    Route::get('conversations/{conversation}', [ConversationController::class, 'show'])->name('ai.conversations.show');
    Route::put('conversations/{conversation}', [ConversationController::class, 'update'])->name('ai.conversations.update');
    Route::delete('conversations/{conversation}', [ConversationController::class, 'destroy'])->name('ai.conversations.destroy');
    Route::post('conversations/{conversation}/archive', [ConversationController::class, 'archive'])->name('ai.conversations.archive');
    
    // Messages
    Route::get('conversations/{conversation}/messages', [ConversationController::class, 'messages'])->name('ai.conversations.messages');
    Route::post('conversations/{conversation}/messages', [ConversationController::class, 'storeMessage'])->name('ai.conversations.messages.store');
    
    // Task Execution
    Route::get('tasks', [AIAssistantController::class, 'getTasks'])->name('ai.tasks.index');
    Route::get('tasks/{task}', [AIAssistantController::class, 'getTask'])->name('ai.tasks.show');
    Route::post('tasks/{task}/retry', [AIAssistantController::class, 'retryTask'])->name('ai.tasks.retry');
    
    // Knowledge Base Search
    Route::get('knowledge/search', [KnowledgeBaseController::class, 'search'])->name('ai.knowledge.search');
    Route::get('knowledge/categories', [KnowledgeBaseController::class, 'categories'])->name('ai.knowledge.categories');
    Route::get('knowledge/documents', [KnowledgeBaseController::class, 'documents'])->name('ai.knowledge.documents');
    Route::get('knowledge/documents/{document}', [KnowledgeBaseController::class, 'show'])->name('ai.knowledge.documents.show');
    
    // AI Assistant Status and Health
    Route::get('status', [AIAssistantController::class, 'status'])->name('ai.status');
    Route::get('health', [AIAssistantController::class, 'health'])->name('ai.health');
    
    // User Preferences
    Route::get('preferences', [AIAssistantController::class, 'getPreferences'])->name('ai.preferences.show');
    Route::put('preferences', [AIAssistantController::class, 'updatePreferences'])->name('ai.preferences.update');
});

// Admin Routes (for knowledge base management)
Route::middleware(['auth:shopify', 'admin'])->prefix('admin/ai-assistant')->group(function () {
    
    // Knowledge Base Management
    Route::get('knowledge', [KnowledgeBaseController::class, 'index'])->name('admin.ai.knowledge.index');
    Route::post('knowledge', [KnowledgeBaseController::class, 'store'])->name('admin.ai.knowledge.store');
    Route::put('knowledge/{document}', [KnowledgeBaseController::class, 'update'])->name('admin.ai.knowledge.update');
    Route::delete('knowledge/{document}', [KnowledgeBaseController::class, 'destroy'])->name('admin.ai.knowledge.destroy');
    
    // Knowledge Base Operations
    Route::post('knowledge/ingest', [KnowledgeBaseController::class, 'ingest'])->name('admin.ai.knowledge.ingest');
    Route::post('knowledge/reindex', [KnowledgeBaseController::class, 'reindex'])->name('admin.ai.knowledge.reindex');
    Route::get('knowledge/stats', [KnowledgeBaseController::class, 'stats'])->name('admin.ai.knowledge.stats');
    
    // AI Configuration
    Route::get('config', [AIAssistantController::class, 'getConfig'])->name('admin.ai.config.show');
    Route::put('config', [AIAssistantController::class, 'updateConfig'])->name('admin.ai.config.update');
    
    // Usage Analytics
    Route::get('analytics', [AIAssistantController::class, 'analytics'])->name('admin.ai.analytics');
    Route::get('analytics/usage', [AIAssistantController::class, 'usageAnalytics'])->name('admin.ai.analytics.usage');
    Route::get('analytics/performance', [AIAssistantController::class, 'performanceAnalytics'])->name('admin.ai.analytics.performance');
});

// Public API Routes (for webhooks and external integrations)
Route::prefix('api/ai-assistant')->group(function () {
    
    // Webhooks
    Route::post('webhooks/openai', [AIAssistantController::class, 'openaiWebhook'])->name('ai.webhooks.openai');
    Route::post('webhooks/pinecone', [AIAssistantController::class, 'pineconeWebhook'])->name('ai.webhooks.pinecone');
    
    // Health Check (public)
    Route::get('ping', [AIAssistantController::class, 'ping'])->name('ai.ping');
});
