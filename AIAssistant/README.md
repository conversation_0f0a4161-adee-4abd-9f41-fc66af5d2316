# AI Assistant Module for eBay-Shopify Integration

This module provides comprehensive AI Assistant functionality for the eBay-Shopify Integration App, including RAG-based conversations, knowledge base management, and task automation.

## Features

### Core Capabilities
- **Profile Configuration Assistance**: AI-powered guidance for eBay category selection and attribute filling
- **Inventory Sync Troubleshooting**: Intelligent diagnosis and resolution of sync issues
- **Platform-Specific Guidance**: Expert knowledge for both Shopify and eBay platforms
- **Task Automation**: Automated execution of common integration tasks
- **RAG Architecture**: Retrieval-Augmented Generation for contextual responses

### Technical Components
- **Vector Database Integration**: Pinecone for semantic search and document retrieval
- **LLM Integration**: OpenAI GPT models for natural language processing
- **Knowledge Base Management**: Document processing and chunking for optimal retrieval
- **Conversation Memory**: Persistent chat history and context management
- **Function Calling**: AI-driven task execution and automation

## Architecture

### Backend Structure
```
Backend/
├── Models/              # Eloquent models for AI data
├── Services/            # Core AI services (OpenAI, Pinecone, etc.)
├── Controllers/         # API controllers for AI endpoints
├── Console/             # Artisan commands for AI operations
├── Providers/           # Service provider for dependency injection
├── Config/              # Configuration files
├── Database/            # Migrations for AI tables
└── Storage/             # Knowledge base documents
```

### Frontend Structure
```
Frontend/
└── Components/          # React components for AI interfaces
```

## Installation

### 1. Backend Setup

#### Copy Files
Copy all files from this module to your Laravel application:

```bash
# Models
cp -r Backend/Models/* app/Models/AI/

# Services  
cp -r Backend/Services/* app/Services/AI/

# Controllers
cp -r Backend/Controllers/* app/Http/Controllers/

# Console Commands
cp -r Backend/Console/* app/Console/Commands/

# Provider
cp Backend/Providers/AIAssistantServiceProvider.php app/Providers/

# Configuration
cp Backend/Config/ai_assistant.php config/

# Migrations
cp Backend/Database/Migrations/* database/migrations/

# Knowledge Base
cp -r Backend/Storage/* storage/
```

#### Register Service Provider
Add the AI Assistant service provider to `config/app.php`:

```php
'providers' => [
    // ... other providers
    App\Providers\AIAssistantServiceProvider::class,
],
```

#### Environment Configuration
Add the following environment variables to your `.env` file:

```env
# AI Assistant Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORGANIZATION=your_openai_org_id
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

PINECONE_API_KEY=your_pinecone_api_key
PINECONE_HOST=https://shopify-ebay-dpl-8kz58od.svc.aped-4627-b74a.pinecone.io
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=shopify-ebay-dpl

AI_CHUNK_SIZE=1000
AI_CHUNK_OVERLAP=200
AI_MAX_RETRIEVED_CHUNKS=10
AI_SIMILARITY_THRESHOLD=0.7

AI_TASKS_ENABLED=true
AI_REQUESTS_PER_MINUTE=60
AI_LOGGING_ENABLED=true
```

#### Run Migrations
```bash
php artisan migrate
```

#### Ingest Knowledge Base
```bash
php artisan ai:ingest-knowledge
```

### 2. Frontend Setup

#### Copy Components
```bash
cp -r Frontend/Components/* frontend/components/
```

#### Add Routes
Add AI Assistant routes to your routing configuration.

### 3. API Routes

Add the following routes to your `routes/api.php`:

```php
// AI Assistant Routes
Route::middleware(['auth:shopify'])->group(function () {
    // Title Suggestions
    Route::get('ai-title-suggestions/shopify-products/{id}', 
        [TitleSuggestionController::class, 'getTitleSuggestion']);
    
    // Chat Interface (when implemented)
    Route::prefix('ai-assistant')->group(function () {
        Route::post('chat', [AIAssistantController::class, 'chat']);
        Route::get('conversations', [AIAssistantController::class, 'getConversations']);
        Route::post('conversations', [AIAssistantController::class, 'createConversation']);
        Route::get('conversations/{id}', [AIAssistantController::class, 'getConversation']);
    });
});
```

## Usage

### Testing AI Services
```bash
# Test OpenAI and Pinecone connections
php artisan ai:test-services

# Test available OpenAI models
php artisan ai:test-models
```

### Knowledge Base Management
```bash
# Ingest knowledge base documents
php artisan ai:ingest-knowledge

# Ingest specific category
php artisan ai:ingest-knowledge --category=troubleshooting

# Force re-ingestion
php artisan ai:ingest-knowledge --force
```

### AI Title Suggestions
The module includes AI-powered eBay title suggestions:

```javascript
// Frontend usage
const response = await fetch(`/api/ai-title-suggestions/shopify-products/${productId}`);
const suggestions = await response.json();
```

## Configuration

### OpenAI Settings
Configure OpenAI models and parameters in `config/ai_assistant.php`:

```php
'openai' => [
    'model' => 'gpt-3.5-turbo',
    'embedding_model' => 'text-embedding-3-small',
    'max_tokens' => 4000,
    'temperature' => 0.7,
],
```

### Knowledge Base Categories
Organize knowledge by categories:

```php
'knowledge_categories' => [
    'app_domain' => ['profile_management', 'sync_operations'],
    'shopify_platform' => ['product_management', 'inventory_tracking'],
    'ebay_platform' => ['category_hierarchy', 'listing_requirements'],
    'troubleshooting' => ['sync_issues', 'api_errors'],
],
```

### Vector Database Settings
Configure Pinecone integration:

```php
'vector_store' => [
    'chunk_size' => 1000,
    'chunk_overlap' => 200,
    'max_retrieved_chunks' => 10,
    'similarity_threshold' => 0.7,
],
```

## Development

### Adding New Knowledge
1. Create markdown files in `storage/ai_knowledge/`
2. Organize by category and subcategory
3. Run `php artisan ai:ingest-knowledge` to process

### Extending AI Functions
1. Add new functions to the `allowed_functions` config
2. Implement function handlers in the appropriate services
3. Update the AI system prompts to include new capabilities

### Custom AI Services
Extend the base services or create new ones by implementing the appropriate interfaces and registering them in the service provider.

## Troubleshooting

### Common Issues
1. **OpenAI API Errors**: Check API key and rate limits
2. **Pinecone Connection Issues**: Verify host URL and API key
3. **Knowledge Ingestion Failures**: Check file permissions and content format
4. **Memory Issues**: Adjust chunk sizes and batch processing limits

### Debugging
Enable detailed logging:
```env
AI_LOGGING_ENABLED=true
LOG_LEVEL=debug
```

### Support
For issues specific to this module, check the logs in `storage/logs/` and ensure all dependencies are properly configured.

## License

This module is part of the eBay-Shopify Integration App and follows the same licensing terms.
